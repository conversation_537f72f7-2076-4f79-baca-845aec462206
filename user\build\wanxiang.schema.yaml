__build_info:
  rime_version: 1.13.1
  timestamps:
    default: 1749012324
    default.custom: 1749539007
    key_bindings: 1749012324
    key_bindings.custom: 0
    punctuation: 1749012323
    punctuation.custom: 0
    wanxiang.custom: 1749613220
    wanxiang.schema: 1749613673
    wanxiang_symbols: 1749482762
    wanxiang_symbols.custom: 0
add_user_dict:
  dictionary: wanxiang
  enable_charset_filter: false
  enable_completion: true
  enable_encoder: true
  enable_sentence: true
  enable_user_dict: true
  encode_commit_history: false
  initial_quality: "-1"
  prefix: "``"
  tag: add_user_dict
  tips: "〔开始造词〕"
  user_dict: zc
birthday_reminder:
  lunar_birthdays:
    "小红": "0815,农历中秋"
    "小蓝": 0114
  solar_birthdays:
    "大明": 0405
    "小明": "0501,准备礼物"
calculator:
  trigger: V
chengyu:
  db_class: stabledb
  dictionary: ""
  enable_completion: false
  enable_sentence: false
  initial_quality: 1.2
  user_dict: "jm_dicts/chengyu"
chinese_english:
  comment_format:
    - "xform/-/ /"
  opencc_config: chinese_english.json
  option_name: chinese_english
  tips: char
cn_en:
  comment_format:
    - "xform/^.+$//"
  db_class: stabledb
  dictionary: ""
  enable_completion: true
  enable_sentence: false
  initial_quality: 0.5
  user_dict: "en_dicts/flypy"
custom_phrase:
  db_class: stabledb
  dictionary: ""
  enable_completion: false
  enable_sentence: false
  initial_quality: 99
  user_dict: custom_phrase_double
editor:
  bindings:
    BackSpace: revert
    "Control+BackSpace": back_syllable
    "Control+Delete": delete_candidate
    "Control+Return": commit_script_text
    "Control+Shift+Return": commit_comment
    Delete: delete
    Escape: cancel
    Return: commit_raw_input
    space: confirm
emoji:
  inherit_comment: false
  opencc_config: emoji.json
  option_name: emoji
engine:
  filters:
    - "lua_filter@*chars_filter"
    - "lua_filter@*super_sequence*F"
    - "lua_filter@*autocap_filter"
    - "reverse_lookup_filter@radical_reverse_lookup"
    - "lua_filter@*super_preedit"
    - "simplifier@emoji"
    - "simplifier@s2t"
    - "simplifier@s2tw"
    - "simplifier@s2hk"
    - "simplifier@chinese_english"
    - "lua_filter@*search@wanxiang_radical"
    - "lua_filter@*super_tips*M"
    - "lua_filter@*super_comment"
    - "lua_filter@*text_formatting"
    - uniquifier
  processors:
    - predictor
    - chord_composer
    - "lua_processor@*super_sequence*P"
    - "lua_processor@*tone_fallback"
    - "lua_processor@*quick_symbol_text"
    - "lua_processor@*super_tips*S"
    - "lua_processor@*limit_repeated"
    - "lua_processor@*backspace_limit"
    - "lua_processor@*userdb_sync_delete"
    - ascii_composer
    - recognizer
    - key_binder
    - "lua_processor@*key_binder"
    - speller
    - punctuator
    - selector
    - navigator
    - express_editor
  segmentors:
    - ascii_segmentor
    - matcher
    - abc_segmentor
    - "affix_segmentor@radical_lookup"
    - "affix_segmentor@reverse_stroke"
    - "affix_segmentor@add_user_dict"
    - punct_segmentor
    - fallback_segmentor
  translators:
    - predict_translator
    - punct_translator
    - script_translator
    - "lua_translator@*shijian"
    - "lua_translator@*unicode"
    - "lua_translator@*number_translator"
    - "lua_translator@*super_calculator"
    - "table_translator@custom_phrase"
    - "table_translator@wanxiang_en"
    - "table_translator@cn_en"
    - "table_translator@chengyu"
    - "table_translator@radical_lookup"
    - "table_translator@reverse_stroke"
    - "script_translator@user_dict_set"
    - "script_translator@add_user_dict"
    - "lua_translator@*force_gc"
fuzhu_cj:
  __append:
    - "xform|^(.*?);.*?;.*?;.*?;.*?;(.*?);.*$|$1;$2|"
fuzhu_flypy:
  __append:
    - "xform|^(.*?);.*?;(.*?);.*$|$1;$2|"
fuzhu_hanxin:
  __append:
    - "xform|^(.*?);.*?;.*?;.*?;.*?;.*?;.*?;.*?;(.*?);.*$|$1;$2|"
fuzhu_jdh:
  __append:
    - "xform|^(.*?);.*?;.*?;.*?;(.*?);.*$|$1;$2|"
fuzhu_kong:
  __append:
    - "xform/;.*$//"
    - "xform/◯/;/"
fuzhu_moqi:
  __append:
    - "xform|^(.*?);(.*?);.*$|$1;$2|"
fuzhu_tiger:
  __append:
    - "xform|^(.*?);.*?;.*?;.*?;.*?;.*?;(.*?);.*$|$1;$2|"
fuzhu_wubi:
  __append:
    - "xform|^(.*?);.*?;.*?;.*?;.*?;.*?;.*?;(.*?);.*$|$1;$2|"
fuzhu_zrm:
  __append:
    - "xform|^(.*?);.*?;.*?;(.*?);.*$|$1;$2|"
grammar:
  collocation_max_length: 8
  collocation_min_length: 2
  collocation_penalty: "-10"
  language: "wanxiang-lts-zh-hans"
  non_collocation_penalty: "-12"
  rear_penalty: "-18"
  weak_collocation_penalty: "-24"
key_binder:
  bindings:
    - {accept: "Control+p", send: Up, when: composing}
    - {accept: "Control+n", send: Down, when: composing}
    - {accept: "Control+b", send: Left, when: composing}
    - {accept: "Control+f", send: Right, when: composing}
    - {accept: "Control+a", send: Home, when: composing}
    - {accept: "Control+e", send: End, when: composing}
    - {accept: "Control+d", send: Delete, when: composing}
    - {accept: "Control+k", send: "Shift+Delete", when: composing}
    - {accept: "Control+h", send: BackSpace, when: composing}
    - {accept: "Control+g", send: Escape, when: composing}
    - {accept: "Control+bracketleft", send: Escape, when: composing}
    - {accept: "Control+y", send: Page_Up, when: composing}
    - {accept: "Alt+v", send: Page_Up, when: composing}
    - {accept: "Control+v", send: Page_Down, when: composing}
    - {accept: ISO_Left_Tab, send: "Shift+Left", when: composing}
    - {accept: "Shift+Tab", send: "Shift+Left", when: composing}
    - {accept: Tab, send: "Shift+Right", when: composing}
    - {accept: minus, send: Page_Up, when: has_menu}
    - {accept: equal, send: Page_Down, when: has_menu}
    - {accept: comma, send: Page_Up, when: paging}
    - {accept: period, send: Page_Down, when: has_menu}
    - {accept: "Control+Shift+1", select: .next, when: always}
    - {accept: "Control+Shift+2", toggle: ascii_mode, when: always}
    - {accept: "Control+Shift+3", toggle: full_shape, when: always}
    - {accept: "Control+Shift+4", toggle: simplification, when: always}
    - {accept: "Control+Shift+5", toggle: extended_charset, when: always}
    - {accept: "Control+Shift+exclam", select: .next, when: always}
    - {accept: "Control+Shift+at", toggle: ascii_mode, when: always}
    - {accept: "Control+Shift+numbersign", toggle: full_shape, when: always}
    - {accept: "Control+Shift+dollar", toggle: simplification, when: always}
    - {accept: "Control+Shift+percent", toggle: extended_charset, when: always}
    - {accept: "Control+w", send: "Control+BackSpace", when: composing}
    - {accept: "Control+e", toggle: chinese_english, when: has_menu}
    - {accept: "Control+c", toggle: chaifen_switch, when: has_menu}
    - {accept: "Control+a", toggle: fuzhu_switch, when: has_menu}
    - {accept: "Control+s", toggle: tone_display, when: has_menu}
    - {accept: "Control+t", toggle: super_tips, when: has_menu}
    - {accept: "Control+g", toggle: charset_filter, when: has_menu}
    - {accept: Tab, send: "Control+Right", when: has_menu}
    - {accept: Tab, send: "Control+Right", when: composing}
    - {accept: "Control+Tab", send_sequence: "{Home}{Shift+Right}{1}{Shift+Right}", when: composing}
    - {accept: "Control+1", send_sequence: "{Home}{Shift+Right}", when: composing}
    - {accept: "Control+2", send_sequence: "{Home}{Shift+Right}{Shift+Right}", when: composing}
    - {accept: "Control+3", send_sequence: "{Home}{Shift+Right}{Shift+Right}{Shift+Right}", when: composing}
    - {accept: "Control+4", send_sequence: "{Home}{Shift+Right}{Shift+Right}{Shift+Right}{Shift+Right}", when: composing}
    - {accept: "Control+5", send_sequence: "{Home}{Shift+Right}{Shift+Right}{Shift+Right}{Shift+Right}{Shift+Right}", when: composing}
    - {accept: "Control+6", send_sequence: "{Home}{Shift+Right}{Shift+Right}{Shift+Right}{Shift+Right}{Shift+Right}{Shift+Right}", when: composing}
    - {accept: "Control+7", send_sequence: "{Home}{Shift+Right}{Shift+Right}{Shift+Right}{Shift+Right}{Shift+Right}{Shift+Right}{Shift+Right}", when: composing}
    - {accept: "`", match: "^.*`$", send_sequence: "{BackSpace}{Home}{`}{`}{End}"}
  import_preset: default
  search: "`"
  shijian_keys:
    - "/"
    - o
  tips_key: period
menu:
  page_size: 6
mohuyin:
  __append:
    - "derive/^z([a-z])/v$1/"
    - "derive/^c([a-z])/i$1/"
    - "derive/^s([a-z])/u$1/"
    - "derive/^v([a-z])/z$1/"
    - "derive/^i([a-z])/c$1/"
    - "derive/^u([a-z])/s$1/"
octagram:
  grammar:
    collocation_max_length: 8
    collocation_min_length: 2
    collocation_penalty: "-10"
    language: "wanxiang-lts-zh-hans"
    non_collocation_penalty: "-12"
    rear_penalty: "-18"
    weak_collocation_penalty: "-24"
  translator:
    contextual_suggestions: false
    max_homographs: 5
    max_homophones: 5
predictor:
  db: "wanxiang-lts-zh-predict.db"
  max_candidates: 5
  max_iterations: 1
punctuator:
  digit_separators: ":,."
  full_shape:
    " ": {commit: "　"}
    "!": {commit: "！"}
    "\"": {pair: ["“", "”"]}
    "#": ["＃", "⌘"]
    "$": ["￥", "$", "€", "£", "¥", "¢", "¤"]
    "%": ["％", "°", "℃"]
    "&": "＆"
    "'": {pair: ["‘", "’"]}
    "(": "（"
    ")": "）"
    "*": ["＊", "·", "・", "×", "※", "❂"]
    "+": "＋"
    ",": {commit: "，"}
    "-": "－"
    .: {commit: "。"}
    "/": ["／", "÷"]
    ":": {commit: "："}
    ";": {commit: "；"}
    "<": ["《", "〈", "«", "‹"]
    "=": "＝"
    ">": ["》", "〉", "»", "›"]
    "?": {commit: "？"}
    "@": ["＠", "☯"]
    "[": ["「", "【", "〔", "［"]
    "\\": ["、", "＼"]
    "]": ["」", "】", "〕", "］"]
    "^": {commit: "……"}
    _: "——"
    "`": "｀"
    "{": ["『", "〖", "｛"]
    "|": ["·", "｜", "§", "¦"]
    "}": ["』", "〗", "｝"]
    "~": "～"
  half_shape:
    "!": "！"
    "\"": {pair: ["“", "”"]}
    "#": "#"
    "$": "¥"
    "%": "%"
    "&": "&"
    "'": {pair: ["‘", "’"]}
    "(": "（"
    ")": "）"
    "*": "*"
    "+": "+"
    ",": "，"
    "-": "-"
    .: "。"
    "/": ["/", "÷"]
    ":": "："
    ";": "；"
    "<": "《"
    "=": "="
    ">": "》"
    "?": "？"
    "@": "@"
    "[": "【"
    "\\": "、"
    "]": "】"
    "^": "……"
    _: "——"
    "`": ["`", "```"]
    "{": "「"
    "|": "|"
    "}": "」"
    "~": "~"
  symbols:
    "/0": ["零", "〇", "⁰", "₀", "⓪", "⓿", "０"]
    "/1": ["一", "壹", "¹", "₁", "Ⅰ", "ⅰ", "①", "➀", "❶", "➊", "⓵", "⑴", "⒈", "１", "㊀", "㈠", "弌", "壱", "幺", "㆒"]
    "/10": ["十", "拾", "¹⁰", "₁₀", "Ⅹ", "ⅹ", "⑩", "➉", "❿", "➓", "⓾", "⑽", "⒑", "１０", "㊉", "㈩", "什"]
    "/2": ["二", "贰", "²", "₂", "Ⅱ", "ⅱ", "②", "➁", "❷", "➋", "⓶", "⑵", "⒉", "２", "㊁", "㈡", "弍", "弐", "貮", "㒃", "㒳", "两", "俩", "㆓"]
    "/3": ["三", "叁", "³", "₃", "Ⅲ", "ⅲ", "③", "➂", "❸", "➌", "⓷", "⑶", "⒊", "３", "㊂", "㈢", "参", "参", "叁", "弎", "仨", "㆔"]
    "/4": ["四", "肆", "⁴", "₄", "Ⅳ", "ⅳ", "④", "➃", "❹", "➍", "⓸", "⑷", "⒋", "４", "㊃", "㈣", "亖"]
    "/5": ["五", "伍", "⁵", "₅", "Ⅴ", "ⅴ", "⑤", "➄", "❺", "➎", "⓹", "⑸", "⒌", "５", "㊄", "㈤", "㐅", "㠪", "𠄡"]
    "/6": ["六", "陆", "⁶", "₆", "Ⅵ", "ⅵ", "⑥", "➅", "❻", "➏", "⓺", "⑹", "⒍", "６", "㊅", "㈥", "ↅ"]
    "/7": ["七", "柒", "⁷", "₇", "Ⅶ", "ⅶ", "⑦", "➆", "❼", "➐", "⓻", "⑺", "⒎", "７", "㊆", "㈦", "漆"]
    "/8": ["八", "捌", "⁸", "₈", "Ⅷ", "ⅷ", "⑧", "➇", "❽", "➑", "⓼", "⑻", "⒏", "８", "㊇", "㈧"]
    "/9": ["九", "玖", "⁹", "₉", "Ⅸ", "ⅸ", "⑨", "➈", "❾", "➒", "⓽", "⑼", "⒐", "９", "㊈", "㈨"]
    "/A": ["Ā", "Á", "Ǎ", "À", "Ȁ", "Â", "Ă", "Ȃ", "Ȧ", "Ä", "Å", "Ã", "ᴀ", "ᴬ", "Ⱥ", "Ả", "Ą", "Ạ", "Ḁ", "Ấ", "Ầ", "Ẫ", "Ẩ", "Ắ", "Ằ", "Ẵ", "Ẳ", "Ǡ", "Ǟ", "Ǻ", "Ậ", "Ặ", "Ɐ", "Ɑ", "Ɒ"]
    "/AA": ["Ꜳ"]
    "/AE": ["Æ", "Ǣ", "Ǽ", "ᴭ", "ᴁ"]
    "/AO": ["Ꜵ"]
    "/AU": ["Ꜷ"]
    "/AV": ["Ꜹ", "Ꜻ"]
    "/AY": ["Ꜽ"]
    "/B": ["Ḃ", "Ḅ", "ʙ", "ᴃ", "ᴮ", "ᴯ", "Ƀ", "Ƃ", "Ḇ", "Ɓ", "Ꞗ"]
    "/C": ["Ç", "Ć", "Č", "Ĉ", "Ċ", "ᴄ", "Ȼ", "Ꞓ", "Ƈ", "Ḉ", "Ꜿ"]
    "/D": ["Ď", "Ḋ", "ᴅ", "ᴆ", "ᴰ", "Đ", "Ƌ", "Ḑ", "Ḓ", "Ḏ", "Ḍ", "Ɖ", "Ɗ", "Ð", "Ǳ", "ǲ", "Ǆ", "ǅ"]
    "/E": ["Ē", "É", "Ě", "È", "Ȅ", "Ê", "Ĕ", "Ȇ", "Ė", "Ë", "Ẽ", "ᴇ", "ᴱ", "Ɇ", "Ẻ", "Ȩ", "Ę", "Ḙ", "Ẹ", "Ḛ", "Ḗ", "Ḕ", "Ế", "Ề", "Ễ", "Ể", "Ḝ", "Ệ", "Ə", "Ɛ", "Ɜ", "Ǝ", "ⱻ", "ᴲ", "Ȝ"]
    "/F": ["Ḟ", "ꜰ", "Ƒ", "Ꞙ", "ꟻ"]
    "/G": ["Ḡ", "Ǵ", "Ǧ", "Ĝ", "Ğ", "Ġ", "ʛ", "ᴳ", "Ǥ", "Ꞡ", "Ģ", "Ɠ", "Ɡ", "Ɣ"]
    "/H": ["Ĥ", "Ȟ", "Ḣ", "Ḧ", "ʜ", "ᴴ", "Ħ", "Ɦ", "Ꜧ", "Ḩ", "Ḫ", "Ḥ", "Ⱨ", "Ɥ", "Ⱶ"]
    "/HV": ["Ƕ"]
    "/I": ["Ī", "Í", "Ǐ", "Ì", "Ȉ", "Î", "Ĭ", "Ȋ", "Ï", "Ĩ", "ɪ", "ᴵ", "ᶦ", "Ɨ", "ᵻ", "ᶧ", "Ỉ", "Į", "Ị", "Ḭ", "Ḯ", "ꟾ", "Ɩ"]
    "/IJ": ["Ĳ"]
    "/J": ["Ĵ", "ᴊ", "ᴶ", "Ɉ", "Ʝ"]
    "/K": ["Ḱ", "Ǩ", "ᴋ", "ᴷ", "Ꝁ", "Ꝃ", "Ꞣ", "Ꝅ", "Ķ", "Ḵ", "Ḳ", "Ƙ", "Ⱪ", "Ʞ"]
    "/L": ["Ĺ", "ʟ", "ᶫ", "Ƚ", "Ꝉ", "Ł", "ᴌ", "Ⱡ", "Ɫ", "Ɬ", "Ľ", "Ļ", "Ḻ", "Ḽ", "Ḷ", "Ŀ", "Ꝇ"]
    "/LL": ["Ỻ"]
    "/M": ["Ḿ", "Ṁ", "ᴍ", "ᴹ", "Ṃ", "Ɱ", "Ɯ", "ꟽ", "ꟿ"]
    "/N": ["Ń", "Ň", "Ǹ", "Ṅ", "Ñ", "ɴ", "ᴺ", "ᴻ", "ᶰ", "Ɲ", "Ƞ", "Ŋ", "Ņ", "Ṉ", "Ṋ", "Ṇ", "Ꞑ"]
    "/NJ": ["Ǌ"]
    "/Nj": ["ǋ"]
    "/O": ["Ō", "Ó", "Ő", "Ǒ", "Ò", "Ô", "Ŏ", "Ȯ", "Ö", "Õ", "ᴏ", "ᴼ", "Ɔ", "ᴐ", "Ø", "Ǫ", "Ọ", "Ơ", "Ɵ", "Ꝋ", "Ꝍ", "Ṓ", "Ṑ", "Ố", "Ồ", "Ỗ", "Ổ", "Ȱ", "Ȫ", "Ȭ", "Ṍ", "Ṏ", "Ộ", "Ǭ", "Ǿ", "Ớ", "Ờ", "Ỡ", "Ở", "Ợ"]
    "/OE": ["Œ", "ɶ"]
    "/OI": ["Ƣ"]
    "/OO": ["Ꝏ"]
    "/OU": ["Ȣ", "ᴽ"]
    "/P": ["Ṕ", "Ṗ", "ᴘ", "ᴾ", "Ᵽ", "Ꝑ", "Ƥ", "Ꝓ", "Ꝕ", "ꟼ"]
    "/Q": ["Ɋ", "Ꝗ", "Ꝙ"]
    "/R": ["Ŕ", "Ř", "Ȑ", "Ȓ", "Ṙ", "ʀ", "ᴙ", "ᴿ", "Ʀ", "ꭆ", "Ɍ", "Ꞧ", "Ŗ", "Ṟ", "Ṛ", "Ṝ", "Ɽ", "ꝶ", "ʶ", "ʁ", "Ꝛ", "Ꝝ"]
    "/Rx": ["℞"]
    "/S": ["Ś", "Ŝ", "Š", "Ṡ", "ꜱ", "Ꞩ", "Ş", "Ṣ", "Ș", "Ṥ", "Ṧ", "Ṩ", "Ʃ", "ẞ"]
    "/T": ["Ť", "Ṫ", "ᴛ", "ᵀ", "Ʈ", "Þ", "Ꝥ", "Ꝧ", "Ŧ", "Ⱦ", "Ţ", "Ṯ", "Ṱ", "Ṭ", "Ț", "Ƭ", "Ʇ"]
    "/TZ": ["Ꜩ"]
    "/U": ["Ū", "Ú", "Ű", "Ǔ", "Ù", "Ȕ", "Û", "Ŭ", "Ȗ", "Ü", "Ǖ", "Ǘ", "Ǚ", "Ǜ", "Ů", "Ũ", "ᴜ", "ᵁ", "ᶸ", "Ʉ", "Ủ", "Ų", "Ṷ", "Ụ", "Ṳ", "Ṵ", "Ư", "Ʊ", "Ṻ", "Ṹ", "Ứ", "Ừ", "Ữ", "Ử", "Ự"]
    "/V": ["Ü", "Ǖ", "Ǘ", "Ǚ", "Ǜ", "Ṽ", "ᴠ", "ⱽ", "Ṿ", "Ꝟ", "Ʋ", "Ỽ", "Ʌ"]
    "/VY": ["Ꝡ"]
    "/W": ["Ẃ", "Ẁ", "Ŵ", "Ẇ", "Ẅ", "W̊", "ᴡ", "ᵂ", "Ẉ", "Ƿ", "Ⱳ"]
    "/X": ["Ẋ", "Ẍ"]
    "/Y": ["Ȳ", "Ý", "Ỳ", "Ŷ", "Ẏ", "Ÿ", "Ỹ", "ʏ", "Ɏ", "Ỷ", "Ỵ", "Ƴ", "Ỿ"]
    "/Z": ["Ź", "Ž", "Ẑ", "Ż", "ᴢ", "Ƶ", "Ẕ", "Ẓ", "Ȥ", "Ⱬ", "Ʒ", "ᴣ", "Ǯ", "Ƹ", "Ɀ", "Ꝣ"]
    "/a": ["ā", "á", "ǎ", "à", "ȁ", "â", "ă", "ȃ", "ȧ", "ä", "å", "ã", "ₐ", "ᵃ", "ª", "ⱥ", "ꬰ", "ả", "ą", "ạ", "ḁ", "ẚ", "ấ", "ầ", "ẫ", "ẩ", "ắ", "ằ", "ẵ", "ẳ", "ǡ", "ǟ", "ǻ", "ậ", "ặ", "ᶏ", "ɐ", "ᵄ", "ɑ", "ᵅ", "ᶐ", "ɒ", "ᶛ"]
    "/aa": ["ꜳ"]
    "/ae": ["æ", "ǣ", "ǽ", "ᵆ", "ᴂ"]
    "/ao": ["ꜵ"]
    "/au": ["ꜷ"]
    "/av": ["ꜹ", "ꜻ"]
    "/ay": ["ꜽ"]
    "/b": ["ḃ", "ḅ", "ᵇ", "ƀ", "ƃ", "ḇ", "ɓ", "ᵬ", "ᶀ", "ꞗ"]
    "/bd": ["、", "。", "「", "」", "『", "』", "【", "】", "〈", "〉", "《", "》", "₋", "⁻", "―", "˗", "ˉ", "＿", "﹍", "﹎", "．", "¡", "‼", "⁉", "¿", "؟", "⁈", "⁇", "､", "｡", "〃", "〄", "々", "〆", "〇", "〒", "〓", "〔", "〕", "〖", "〗", "〘", "〙", "〚", "〛", "〜", "〝", "〞", "〟", "〠", "〰", "〱", "〲", "〳", "〴", "〵", "〶", "〷", "〻", "〼", "〽"]
    "/bdz": ["﹅", "﹆", "﹁", "﹂", "﹃", "﹄", "︙", "︱", "︻", "︼", "︗", "︘", "︵", "︶", "︷", "︸", "︹", "︺", "︿", "﹀", "︽", "︾", "︰", "︲", "︳", "︴", "﹉", "﹊", "﹋", "﹌", "﹍", "﹎", "﹏", "﹇", "﹈", "︐", "︑", "︒", "︔", "︕", "︖"]
    "/bdzy": ["‐", "‑", "‒", "–", "—", "―", "‖", "‗", "‘", "’", "‚", "‛", "“", "”", "„", "‟", "†", "‡", "•", "‣", "․", "‥", "…", "‧", "‰", "‱", "′", "″", "‴", "‵", "‶", "‷", "‸", "‹", "›", "※", "‼", "‽", "‾", "‿", "⁀", "⁁", "⁂", "⁃", "⁄", "⁅", "⁆", "⁇", "⁈", "⁉", "⁊", "⁋", "⁌", "⁍", "⁎", "⁏", "⁐", "⁑", "⁒", "⁓", "⁔", "⁕", "⁖", "⁗", "⁘", "⁙", "⁚", "⁛", "⁜", "⁝", "⁞"]
    "/bg": ["☰", "☱", "☲", "☳", "☴", "☵", "☶", "☷"]
    "/bgm": ["乾", "兑", "离", "震", "巽", "坎", "艮", "坤"]
    "/bh": ["㇀", "㇁", "㇂", "㇃", "㇄", "㇅", "㇆", "㇇", "㇈", "㇉", "㇊", "㇋", "㇌", "㇍", "㇎", "㇏", "㇐", "㇑", "㇒", "㇓", "㇔", "㇕", "㇖", "㇗", "㇘", "㇙", "㇚", "㇛", "㇜", "㇝", "㇞", "㇟", "㇠", "㇡", "㇢", "㇣"]
    "/bq": ["☻", "☺", "☹"]
    "/c": ["ç", "ć", "č", "ĉ", "ċ", "ᶜ", "ȼ", "ꞓ", "ƈ", "ḉ", "ꞔ", "ɕ", "ᶝ", "ꜿ"]
    "/d": ["ď", "ḋ", "ᵈ", "đ", "ƌ", "ᵭ", "ḑ", "ḓ", "ḏ", "ḍ", "ɖ", "ɗ", "ᶑ", "ᶁ", "ð", "ᶞ", "ꝱ", "ʤ", "ʣ", "ʥ", "ȡ", "ƍ", "ǳ", "ǆ", "ẟ"]
    "/db": ["ȸ"]
    "/dh": ["。", "．", "，", "、", "：", "；", "！", "‼", "？", "⁇"]
    "/dn": ["❖", "⌘", "⌃", "⌥", "⎇", "⇧", "⇪", "␣", "⇥", "⇤", "↩", "⌅", "⌤", "⌫", "⌦", "⌧", "⎋", "⌨", "◁", "⌀", "⌖", "⌗", "⏏", "↖", "↘", "⇞", "⇟", "⌚", "⏰", "⏱", "⏲", "⏳", "⌛", "⌜", "⌝⌞⌟", "⍑", "⏩", "⏪", "⏫", "⏬", "⏭", "⏮", "⏯"]
    "/dw": ["Å", "℃", "％", "‰", "‱", "°", "℉", "㏃", "㏆", "㎈", "㏄", "㏅", "㎝", "㎠", "㎤", "㏈", "㎗", "㎙", "㎓", "㎬", "㏉", "㏊", "㏋", "㎐", "㏌", "㎄", "㎅", "㎉", "㎏", "㎑", "㏍", "㎘", "㎞", "㏎", "㎢", "㎦", "㎪", "㏏", "㎸", "㎾", "㏀", "㏐", "㏓", "㎧", "㎨", "㎡", "㎥", "㎃", "㏔", "㎆", "㎎", "㎒", "㏕", "㎖", "㎜", "㎟", "㎣", "㏖", "㎫", "㎳", "㎷", "㎹", "㎽", "㎿", "㏁", "㎁", "㎋", "㎚", "㎱", "㎵", "㎻", "㏘", "㎩", "㎀", "㎊", "㏗", "㏙", "㏚", "㎰", "㎴", "㎺", "㎭", "㎮", "㎯", "㏛", "㏜", "㎔", "㏝", "㎂", "㎌", "㎍", "㎕", "㎛", "㎲", "㎶", "㎼"]
    "/dz": ["子", "丑", "寅", "卯", "辰", "巳", "午", "未", "申", "酉", "戌", "亥"]
    "/e": ["ē", "é", "ě", "è", "ȅ", "ê", "ĕ", "ȇ", "ė", "ë", "ẽ", "ₑ", "ᵉ", "ɇ", "ꬳ", "ẻ", "ȩ", "ę", "ḙ", "ẹ", "ḛ", "ḗ", "ḕ", "ế", "ề", "ễ", "ể", "ḝ", "ệ", "ᶒ", "ꬴ", "ɘ", "ə", "ɚ", "ᶕ", "ɛ", "ᵋ", "ᶓ", "ɜ", "ᵌ", "ᴈ", "ᶟ", "ɝ", "ᶔ", "ɞ", "ʚ", "ǝ", "ₔ", "ᵊ", "ȝ", "ⱸ"]
    "/ey": ["а", "б", "в", "г", "д", "е", "ё", "ж", "з", "и", "й", "к", "л", "м", "н", "о", "п", "р", "с", "т", "у", "ф", "х", "ц", "ч", "ш", "щ", "ъ", "ы", "ь", "э", "ю", "я"]
    "/eyd": ["А", "Б", "В", "Г", "Д", "Е", "Ё", "Ж", "З", "И", "Й", "К", "Л", "М", "Н", "О", "П", "Р", "С", "Т", "У", "Ф", "Х", "Ц", "Ч", "Ш", "Щ", "Ъ", "Ы", "Ь", "Э", "Ю", "Я"]
    "/f": ["ḟ", "ᶠ", "ƒ", "ᵮ", "ᶂ", "ꞙ"]
    "/ff": ["ﬀ"]
    "/ffi": ["ﬃ"]
    "/ffl": ["ﬄ"]
    "/fh": ["©", "®", "℗", "℠", "™", "℡", "℻", "☇", "☈", "☉", "☊", "☋", "☌", "☍", "☎", "☏", "☐", "☑", "☒", "☓", "☕", "☖", "☗", "⛉", "⛊", "☘", "☙", "☚", "☛", "☜", "☝", "☞", "☟", "☠", "☡", "☢", "☣", "☤", "☥", "☦", "☧", "☨", "☩", "☪", "☫", "☬", "☭", "☮", "☯", "☸", "♨", "♰", "♱", "♲", "♳", "♴", "♵", "♶", "♷", "♸", "♹", "♺", "♻", "♼", "♽", "♾", "♿", "⚆", "⚇", "⚈", "⚉", "⚐", "⚑", "⚒", "⚓", "⚔", "⚕", "⚖", "⚗", "⚘", "⚙", "⚚", "⚛", "⚜", "⚝", "⚞", "⚟", "⚠", "⚡", "⚰", "⚱", "⚲", "⚳", "⚴", "⚵", "⚶", "⚷", "⚸", "⚹", "⚺", "⚻", "⚼", "⚽", "⚾", "⚿", "⛀", "⛁", "⛂", "⛃", "⛋", "⛌", "⛍", "⛎", "⛏", "⛐", "⛑", "⛒", "⛓", "⛔", "⛕", "⛖", "⛗", "⛘", "⛙", "⛚", "⛛", "⛜", "⛝", "⛞", "⛟", "⛠", "⛡", "⛢", "⛣", "⛨", "⛩", "⛪", "⛫", "⛬", "⛭", "⛮", "⛯", "⛰", "⛱", "⛲", "⛳", "⛴", "⛵", "⛶", "⛷", "⛸", "⛹", "⛺", "⛻", "⛼", "⛽", "⛾", "⛿"]
    "/fi": ["ﬁ"]
    "/fj": ["⸺", "——", "……", "⋯⋯", "～", "-", "–", "—", "·", "・", "‧", "/", "／", "＼", "｜"]
    "/fk": ["▀", "▁", "▂", "▃", "▄", "▅", "▆", "▇", "█", "▉", "▊", "▋", "▌", "▍", "▎", "▏", "▐", "░", "▒", "▓", "▔", "▕", "▖", "▗", "▘", "▙", "▚", "▛", "▜", "▝", "▞", "▟"]
    "/fl": ["ﬂ"]
    "/fn": ["ʩ"]
    "/fs": ["⅟", "½", "↉", "⅓", "⅔", "¼", "⅕", "⅖", "⅗", "⅘", "⅙", "⅚", "⅐", "⅛", "⅜", "⅝", "⅞", "⅑", "⅒"]
    "/g": ["ḡ", "ǵ", "ǧ", "ĝ", "ğ", "ġ", "ᵍ", "ǥ", "ꞡ", "ģ", "ɠ", "ᵷ", "ᶃ", "ɡ", "ꬶ", "ᶢ", "ɣ", "ˠ", "ɤ", "ᵹ"]
    "/gz": ["甲子", "乙丑", "丙寅", "丁卯", "戊辰", "己巳", "庚午", "辛未", "壬申", "癸酉", "甲戌", "乙亥", "丙子", "丁丑", "戊寅", "己卯", "庚辰", "辛巳", "壬午", "癸未", "甲申", "乙酉", "丙戌", "丁亥", "戊子", "己丑", "庚寅", "辛卯", "壬辰", "癸巳", "甲午", "乙未", "丙申", "丁酉", "戊戌", "己亥", "庚子", "辛丑", "壬寅", "癸卯", "甲辰", "乙巳", "丙午", "丁未", "戊申", "己酉", "庚戌", "辛亥", "壬子", "癸丑", "甲寅", "乙卯", "丙辰", "丁巳", "戊午", "己未", "庚申", "辛酉", "壬戌", "癸亥"]
    "/h": ["ĥ", "ȟ", "ḣ", "ḧ", "ͪ", "ħ", "ɦ", "ʱ", "ꜧ", "ꭜ", "ɧ", "ḩ", "ẖ", "ḫ", "ḥ", "ⱨ", "ꞕ", "ɥ", "ᶣ", "ʮ", "ʯ", "ⱶ"]
    "/hb": ["￥", "¥", "¤", "￠", "＄", "$", "￡", "£", "৳", "฿", "₠", "₡", "₢", "₣", "₤", "₥", "₦", "₧", "₩", "₪", "₫", "€", "₭", "₮", "₯", "₰", "₱", "₲", "₳", "₴", "₵", "₶", "₷", "₸", "₹", "₺", "₨", "﷼"]
    "/hj": ["＿", "﹏", "●", "•"]
    "/hv": ["ƕ"]
    "/hw": ["ㄱ", "ㄴ", "ㄷ", "ㄹ", "ㅁ", "ㅂ", "ㅅ", "ㅇ", "ㅈ", "ㅊ", "ㅋ", "ㅌ", "ㅍ", "ㅎ"]
    "/hwh": ["㈀", "㈁", "㈂", "㈃", "㈄", "㈅", "㈆", "㈇", "㈈", "㈉", "㈊", "㈋", "㈌", "㈍", "㈎", "㈏", "㈐", "㈑", "㈒", "㈓", "㈔", "㈕", "㈖", "㈗", "㈘", "㈙", "㈚", "㈛", "㈜", "㈝", "㈞"]
    "/hwq": ["㉠", "㉡", "㉢", "㉣", "㉤", "㉥", "㉦", "㉧", "㉨", "㉩", "㉪", "㉫", "㉬", "㉭", "㉮", "㉯", "㉰", "㉱", "㉲", "㉳", "㉴", "㉵", "㉶", "㉷", "㉸", "㉹", "㉺", "㉻", "㉼", "㉽", "㉾", "㉿"]
    "/hzh": ["㈠", "㈡", "㈢", "㈣", "㈤", "㈥", "㈦", "㈧", "㈨", "㈩", "㈪", "㈫", "㈬", "㈭", "㈮", "㈯", "㈰", "㈱", "㈲", "㈳", "㈴", "㈵", "㈶", "㈷", "㈸", "㈹", "㈺", "㈻", "㈼", "㈽", "㈾", "㈿", "㉀", "㉁", "㉂", "㉃"]
    "/hzq": ["㊀", "㊁", "㊂", "㊃", "㊄", "㊅", "㊆", "㊇", "㊈", "㊉", "㊊", "㊋", "㊌", "㊍", "㊎", "㊏", "㊐", "㊑", "㊒", "㊓", "㊔", "㊕", "㊖", "㊗", "㊘", "㊙", "㊚", "㊛", "㊜", "㊝", "㊞", "㊟", "㊠", "㊡", "㊢", "㊣", "㊤", "㊥", "㊦", "㊧", "㊨", "㊩", "㊪", "㊫", "㊬", "㊭", "㊮", "㊯", "㊰", "㉄", "㉅", "㉆", "㉇"]
    "/i": ["ī", "í", "ǐ", "ì", "ȉ", "î", "ĭ", "ȋ", "ï", "ĩ", "ᵢ", "ı", "ɨ", "ᶤ", "ỉ", "į", "ị", "ḭ", "ᴉ", "ᵎ", "ḯ", "ᶖ", "ɩ", "ᶥ", "ᵼ"]
    "/ij": ["ĳ"]
    "/iro": ["い", "ろ", "は", "に", "ほ", "へ", "と", "ち", "り", "ぬ", "る", "を", "わ", "か", "よ", "た", "れ", "そ", "つ", "ね", "な", "ら", "む", "う", "ゐ", "の", "お", "く", "や", "ま", "け", "ふ", "こ", "え", "て", "あ", "さ", "き", "ゆ", "め", "み", "し", "ゑ", "ひ", "も", "せ", "す"]
    "/j": ["ĵ", "ǰ", "ⱼ", "ʲ", "ɉ", "ȷ", "ɟ", "ᶡ", "ʄ", "ʝ", "ᶨ"]
    "/jg": ["⿰", "⿱", "⿲", "⿳", "⿴", "⿵", "⿶", "⿷", "⿸", "⿹", "⿺", "⿻", "〾"]
    "/jh": ["■", "□", "▢", "▣", "▤", "▥", "▦", "▧", "▨", "▩", "▪", "▫", "▬", "▭", "▮", "▯", "▰", "▱", "▲", "△", "▴", "▵", "▶", "▷", "▸", "▹", "►", "▻", "▼", "▽", "▾", "▿", "◀", "◁", "◂", "◃", "◄", "◅", "◆", "◇", "◈", "◉", "◊", "○", "◌", "◍", "◎", "●", "◐", "◑", "◒", "◓", "◔", "◕", "◖", "◗", "◘", "◙", "◚", "◛", "◜", "◝", "◞", "◟", "◠", "◡", "◢", "◣", "◤", "◥", "◦", "◧", "◨", "◩", "◪", "◫", "◬", "◭", "◮", "◯", "◰", "◱", "◲", "◳", "◴", "◵", "◶", "◷", "◸", "◹", "◺", "◻", "◼", "◽", "◾", "◿"]
    "/jm": ["あ", "ぁ", "い", "ぃ", "う", "ぅ", "え", "ぇ", "お", "ぉ", "か", "ゕ", "が", "き", "ぎ", "く", "ぐ", "け", "ゖ", "げ", "こ", "ご", "さ", "ざ", "し", "じ", "す", "ず", "せ", "ぜ", "そ", "ぞ", "た", "だ", "ち", "ぢ", "つ", "っ", "づ", "て", "で", "と", "ど", "な", "に", "ぬ", "ね", "の", "は", "ば", "ぱ", "ひ", "び", "ぴ", "ふ", "ぶ", "ぷ", "へ", "べ", "ぺ", "ほ", "ぼ", "ぽ", "ま", "み", "む", "め", "も", "や", "ゃ", "ゆ", "ゅ", "よ", "ょ", "ら", "り", "る", "れ", "ろ", "わ", "ゎ", "ゐ", "ゔ", "ゑ", "を", "ん", "・", "ー", "ゝ", "ゞ", "ゟ"]
    "/jma": ["あ", "か", "が", "さ", "ざ", "た", "だ", "な", "は", "ば", "ぱ", "ま", "や", "ら", "わ", "ア", "カ", "ガ", "サ", "ザ", "タ", "ダ", "ナ", "ハ", "バ", "パ", "マ", "ヤ", "ラ", "ワ"]
    "/jmb": ["ば", "び", "ぶ", "べ", "ぼ", "バ", "ビ", "ブ", "ベ", "ボ"]
    "/jmbj": ["ｱ", "ｧ", "ｲ", "ｨ", "ｳ", "ｩ", "ｴ", "ｪ", "ｵ", "ｫ", "ｶ", "ｷ", "ｸ", "ｹ", "ｺ", "ｻ", "ｼ", "ｽ", "ｾ", "ｿ", "ﾀ", "ﾁ", "ﾂ", "ｯ", "ﾃ", "ﾄ", "ﾅ", "ﾆ", "ﾇ", "ﾈ", "ﾉ", "ﾊ", "ﾋ", "ﾌ", "ﾍ", "ﾎ", "ﾏ", "ﾐ", "ﾑ", "ﾒ", "ﾓ", "ﾔ", "ｬ", "ﾕ", "ｭ", "ﾖ", "ｮ", "ﾗ", "ﾘ", "ﾙ", "ﾚ", "ﾛ", "ﾜ", "ｦ", "ﾝ", "･", "ｰ", "ﾞ", "ﾟ"]
    "/jmd": ["だ", "ぢ", "づ", "で", "ど", "ダ", "ヂ", "ヅ", "デ", "ド"]
    "/jme": ["え", "け", "げ", "せ", "ぜ", "て", "で", "ね", "へ", "べ", "ぺ", "め", "れ", "ゑ", "エ", "ケ", "ゲ", "セ", "ゼ", "テ", "デ", "ネ", "ヘ", "ベ", "ペ", "メ", "レ", "ヱ"]
    "/jmg": ["が", "ぎ", "ぐ", "げ", "ご", "ガ", "ギ", "グ", "ゲ", "ゴ"]
    "/jmh": ["は", "ひ", "ふ", "へ", "ほ", "ハ", "ヒ", "フ", "ヘ", "ホ"]
    "/jmi": ["い", "き", "ぎ", "し", "じ", "ち", "ぢ", "に", "ひ", "び", "ぴ", "み", "り", "ゐ", "イ", "キ", "ギ", "シ", "ジ", "チ", "ヂ", "ニ", "ヒ", "ビ", "ピ", "ミ", "リ", "ヰ"]
    "/jmk": ["か", "ゕ", "き", "く", "け", "ゖ", "こ", "カ", "ヵ", "キ", "ク", "ケ", "ヶ", "コ"]
    "/jmm": ["ま", "み", "む", "め", "も", "マ", "ミ", "ム", "メ", "モ"]
    "/jmn": ["な", "に", "ぬ", "ね", "の", "ん", "ナ", "ニ", "ヌ", "ネ", "ノ", "ン"]
    "/jmo": ["お", "こ", "ご", "そ", "ぞ", "と", "ど", "の", "ほ", "ぼ", "ぽ", "も", "ろ", "を", "オ", "コ", "ゴ", "ソ", "ゾ", "ト", "ド", "ノ", "ホ", "ボ", "ポ", "モ", "ロ", "ヲ"]
    "/jmp": ["ぱ", "ぴ", "ぷ", "ぺ", "ぽ", "パ", "ピ", "プ", "ペ", "ポ"]
    "/jmq": ["㋐", "㋑", "㋒", "㋓", "㋔", "㋕", "㋖", "㋗", "㋘", "㋙", "㋚", "㋛", "㋜", "㋝", "㋞", "㋟", "㋠", "㋡", "㋢", "㋣", "㋤", "㋥", "㋦", "㋧", "㋨", "㋩", "㋪", "㋫", "㋬", "㋭", "㋮", "㋯", "㋰", "㋱", "㋲", "㋳", "㋴", "㋵", "㋶", "㋷", "㋸", "㋹", "㋺", "㋻", "㋼", "㋽", "㋾"]
    "/jmr": ["ら", "り", "る", "れ", "ろ", "ラ", "リ", "ル", "レ", "ロ"]
    "/jms": ["さ", "し", "す", "せ", "そ", "サ", "シ", "ス", "セ", "ソ"]
    "/jmt": ["た", "ち", "つ", "っ", "て", "と", "タ", "チ", "ツ", "ッ", "テ", "ト"]
    "/jmu": ["う", "く", "ぐ", "す", "ず", "つ", "づ", "ぬ", "ふ", "ぶ", "ぷ", "む", "る", "ウ", "ク", "グ", "ス", "ズ", "ツ", "ヅ", "ヌ", "フ", "ブ", "プ", "ム", "ル"]
    "/jmw": ["わ", "ゐ", "ゑ", "を", "ワ", "ヰ", "ヱ", "ヲ"]
    "/jmy": ["や", "ゃ", "ゆ", "ゅ", "よ", "ょ", "ヤ", "ャ", "ユ", "ュ", "ヨ", "ョ"]
    "/jmz": ["ざ", "じ", "ず", "ぜ", "ぞ", "ザ", "ジ", "ズ", "ゼ", "ゾ"]
    "/jq": ["立春", "雨水", "惊蛰", "春分", "清明", "谷雨", "立夏", "小满", "芒种", "夏至", "小暑", "大暑", "立秋", "处暑", "白露", "秋分", "寒露", "霜降", "立冬", "小雪", "大雪", "冬至", "小寒", "大寒"]
    "/jt": ["↑", "↓", "←", "→", "↕", "↔", "↖", "↗", "↙", "↘", "↚", "↛", "↮", "↜", "↝", "↞", "↟", "↠", "↡", "↢", "↣", "↤", "↥", "↦", "↧", "↨", "↩", "↪", "↫", "↬", "↭", "↯", "↰", "↱", "↲", "↳", "↴", "↵", "↶", "↷", "↸", "↹", "↺", "↻", "↼", "↽", "↾", "↿", "⇀", "⇁", "⇂", "⇃", "⇄", "⇅", "⇆", "⇇", "⇈", "⇉", "⇊", "⇋", "⇌", "⇐", "⇍", "⇑", "⇒", "⇏", "⇓", "⇔", "⇎", "⇕", "⇖", "⇗", "⇘", "⇙", "⇚", "⇛", "⇜", "⇝", "⇞", "⇟", "⇠", "⇡", "⇢", "⇣", "⇤", "⇥", "⇦", "⇧", "⇨", "⇩", "⇪", "⇫", "⇬", "⇭", "⇮", "⇯", "⇰", "⇱", "⇲", "⇳", "⇴", "⇵", "⇶", "⇷", "⇸", "⇹", "⇺", "⇻", "⇼", "⇽", "➔", "➘", "➙", "➚", "➛", "➜", "➝", "➞", "➟", "➠", "➡", "➢", "➣", "➤", "➥", "➦", "➧", "➨", "➩", "➪", "➫", "➬", "➭", "➮", "➱", "➲", "➳", "➴", "➵", "➶", "➷", "➸", "➹", "➺", "➻", "➼", "➽", "➾"]
    "/jz": ["「", "」", "『", "』", "“", "”", "‘", "’", "（", "）", "《", "》", "〈", "〉", "【", "】", "〖", "〗", "〔", "〕", "［", "］", "｛", "｝", "«", "»", "‹", "›", "⟨", "⟩"]
    "/k": ["ḱ", "ǩ", "ₖ", "ᵏ", "ꝁ", "ꝃ", "ꞣ", "ꝅ", "ķ", "ḵ", "ḳ", "ƙ", "ᶄ", "ⱪ", "ʞ", "ĸ"]
    "/kx": ["一", "丨", "丶", "丿", "乙", "亅", "二", "亠", "人", "儿", "入", "八", "冂", "冖", "冫", "几", "凵", "刀", "力", "勹", "匕", "匚", "匸", "十", "卜", "卩", "厂", "厶", "又", "口", "囗", "土", "士", "夂", "夊", "夕", "大", "女", "子", "宀", "寸", "小", "尢", "尸", "屮", "山", "巛", "工", "己", "巾", "干", "幺", "广", "廴", "廾", "弋", "弓", "彐", "彡", "彳", "心", "戈", "戶", "手", "支", "攴", "文", "斗", "斤", "方", "无", "日", "曰", "月", "木", "欠", "止", "歹", "殳", "毋", "比", "毛", "氏", "气", "水", "火", "爪", "父", "爻", "爿", "片", "牙", "牛", "犬", "玄", "玉", "瓜", "瓦", "甘", "生", "用", "田", "疋", "疒", "癶", "白", "皮", "皿", "目", "矛", "矢", "石", "示", "禸", "禾", "穴", "立", "竹", "米", "糸", "缶", "网", "羊", "羽", "老", "而", "耒", "耳", "聿", "肉", "臣", "自", "至", "臼", "舌", "舛", "舟", "艮", "色", "艸", "虍", "虫", "血", "行", "衣", "襾", "見", "角", "言", "谷", "豆", "豕", "豸", "貝", "赤", "走", "足", "身", "車", "辛", "辰", "辵", "邑", "酉", "釆", "里", "金", "長", "門", "阜", "隶", "隹", "雨", "靑", "非", "面", "革", "韋", "韭", "音", "頁", "風", "飛", "食", "首", "香", "馬", "骨", "高", "髟", "鬥", "鬯", "鬲", "鬼", "魚", "鳥", "鹵", "鹿", "麥", "麻", "黃", "黍", "黑", "黹", "黽", "鼎", "鼓", "鼠", "鼻", "齊", "齒", "龍", "龜", "龠"]
    "/l": ["ĺ", "ˡ", "ł", "ꝉ", "ƚ", "ⱡ", "ɫ", "ꭞ", "ꬸ", "ɬ", "ľ", "ļ", "ḻ", "ḽ", "ḷ", "ŀ", "ꝲ", "ƛ", "ᶅ", "ᶪ", "ɭ", "ᶩ", "ḹ", "ꬷ", "ꭝ", "ꬹ", "ȴ", "ꝇ"]
    "/lj": ["ǉ"]
    "/ll": ["ỻ"]
    "/lm": ["ⅰ", "ⅱ", "ⅲ", "ⅳ", "ⅴ", "ⅵ", "ⅶ", "ⅷ", "ⅸ", "ⅹ", "ⅺ", "ⅻ", "ⅼ", "ⅽ", "ⅾ", "ⅿ"]
    "/lmd": ["Ⅰ", "Ⅱ", "Ⅲ", "Ⅳ", "Ⅴ", "Ⅵ", "Ⅶ", "Ⅷ", "Ⅸ", "Ⅹ", "Ⅺ", "Ⅻ", "Ⅼ", "Ⅽ", "Ⅾ", "Ⅿ"]
    "/ls": ["ʪ"]
    "/lssg": ["䷀", "䷁", "䷂", "䷃", "䷄", "䷅", "䷆", "䷇", "䷈", "䷉", "䷊", "䷋", "䷌", "䷍", "䷎", "䷏", "䷐", "䷑", "䷒", "䷓", "䷔", "䷕", "䷖", "䷗", "䷘", "䷙", "䷚", "䷛", "䷜", "䷝", "䷞", "䷟", "䷠", "䷡", "䷢", "䷣", "䷤", "䷥", "䷦", "䷧", "䷨", "䷩", "䷪", "䷫", "䷬", "䷭", "䷮", "䷯", "䷰", "䷱", "䷲", "䷳", "䷴", "䷵", "䷶", "䷷", "䷸", "䷹", "䷺", "䷻", "䷼", "䷽", "䷾", "䷿"]
    "/lssgm": ["乾", "坤", "屯", "蒙", "需", "讼", "师", "比", "小畜", "履", "泰", "否", "同人", "大有", "谦", "豫", "随", "蛊", "临", "观", "噬嗑", "贲", "剥", "复", "无妄", "大畜", "颐", "大过", "坎", "离", "咸", "恒", "遯", "大壮", "晋", "明夷", "家人", "睽", "蹇", "解", "损", "益", "夬", "姤", "萃", "升", "困", "井", "革", "鼎", "震", "艮", "渐", "归妹", "丰", "旅", "巽", "兑", "涣", "节", "中孚", "小过", "既济", "未济"]
    "/lx": ["♂", "♀", "⚢", "⚣", "⚤", "⚥", "⚦", "⚧", "⚨", "⚩", "⚪", "⚫", "⚬", "⚭", "⚮", "⚯"]
    "/lz": ["ʫ", "ɮ"]
    "/m": ["ḿ", "ṁ", "ᵐ", "ₘ", "ṃ", "ᵯ", "ɱ", "ᶬ", "ꬺ", "ᶆ", "ꝳ", "ɯ", "ᵚ", "ɰ", "ᶭ", "ᴟ"]
    "/mj": ["🀀", "🀁", "🀂", "🀃", "🀄", "🀅", "🀆", "🀇", "🀈", "🀉", "🀊", "🀋", "🀌", "🀍", "🀎", "🀏", "🀐", "🀑", "🀒", "🀓", "🀔", "🀕", "🀖", "🀗", "🀘", "🀙", "🀚", "🀛", "🀜", "🀝", "🀞", "🀟", "🀠", "🀡", "🀢", "🀣", "🀤", "🀥", "🀦", "🀧", "🀨", "🀩", "🀪", "🀫"]
    "/n": ["ń", "ň", "ǹ", "ṅ", "ñ", "ₙ", "ⁿ", "ɲ", "ᶮ", "ɳ", "ᶯ", "ȵ", "ƞ", "ŋ", "ᵑ", "ꬻ", "ꬼ", "ꝴ", "ŉ", "ꞥ", "ņ", "ṉ", "ṋ", "ṇ", "ᵰ", "ꞑ", "ᶇ"]
    "/nj": ["ǌ"]
    "/num": ["№"]
    "/o": ["ō", "ó", "ǒ", "ò", "ő", "ô", "ŏ", "ȯ", "ö", "õ", "ₒ", "ᵒ", "º", "ɔ", "ᵓ", "ᶗ", "ꬿ", "ø", "ǫ", "ọ", "ơ", "ɵ", "ᶱ", "ᴑ", "ᴒ", "ᴓ", "ꝋ", "ꝍ", "ṓ", "ṑ", "ố", "ồ", "ỗ", "ổ", "ȱ", "ȫ", "ȭ", "ṍ", "ṏ", "ộ", "ǭ", "ǿ", "ớ", "ờ", "ỡ", "ở", "ợ", "ɷ", "ⱺ", "ᴖ", "ᵔ", "ᴗ", "ᵕ"]
    "/oe": ["œ", "ᴔ"]
    "/oi": ["ƣ"]
    "/oo": ["ꝏ"]
    "/ou": ["ȣ"]
    "/p": ["ṕ", "ṗ", "ᵖ", "ᵽ", "ꝑ", "ᵱ", "ƥ", "ᶈ", "ꝓ", "ꝕ", "ɸ", "ᶲ", "ⱷ"]
    "/pjm": ["ア", "ァ", "イ", "ィ", "ウ", "ゥ", "エ", "ェ", "オ", "ォ", "カ", "ヵ", "ガ", "キ", "ギ", "ク", "グ", "ケ", "ヶ", "ゲ", "コ", "ゴ", "サ", "ザ", "シ", "ジ", "ス", "ズ", "セ", "ゼ", "ソ", "ゾ", "タ", "ダ", "チ", "ヂ", "ツ", "ッ", "ヅ", "テ", "デ", "ト", "ド", "ナ", "ニ", "ヌ", "ネ", "ノ", "ハ", "バ", "パ", "ヒ", "ビ", "ピ", "フ", "ブ", "プ", "ヘ", "ベ", "ペ", "ホ", "ボ", "ポ", "マ", "ミ", "ム", "メ", "モ", "ヤ", "ャ", "ユ", "ュ", "ヨ", "ョ", "ラ", "リ", "ル", "レ", "ロ", "ワ", "ヮ", "ヰ", "ヸ", "ヴ", "ヱ", "ヹ", "ヲ", "ヺ", "ン", "・", "ー", "ヽ", "ヾ", "ヿ", "ㇰ", "ㇱ", "ㇲ", "ㇳ", "ㇴ", "ㇵ", "ㇶ", "ㇷ", "ㇸ", "ㇹ", "ㇺ", "ㇻ", "ㇼ", "ㇽ", "ㇾ", "ㇿ"]
    "/pk": ["♠", "♥", "♣", "♦", "♤", "♡", "♧", "♢"]
    "/pp": ["乛", "冫", "丷", "龹", "⺌", "龸", "亻", "亼", "亽", "仒", "冖", "冂", "冃", "冄", "宀", "罒", "㓁", "罓", "冈", "凵", "厶", "刂", "勹", "匚", "匸", "卩", "阝", "厂", "丆", "广", "壬", "訁", "讠", "釒", "钅", "飠", "饣", "龺", "攵", "夂", "夊", "尢", "尣", "兂", "旡", "巜", "巛", "彐", "彑", "彡", "彳", "龰", "辶", "廴", "㞢", "忄", "㣺", "扌", "爫", "龵", "廾", "歺", "癶", "氵", "氺", "火", "灬", "爿", "丬", "疒", "牜", "⺶", "犭", "豕", "豸", "虍", "艹", "卝", "龷", "丗", "龶", "芈", "丵", "菐", "黹", "礻", "衤", "糸", "糹", "纟", "龻", "镸", "髟", "襾", "覀", "吅", "㗊", "㠭", "㸚", "叕"]
    "/py": ["ā", "á", "ǎ", "à", "ō", "ó", "ǒ", "ò", "ê", "ê̄", "ế", "ê̌", "ề", "ē", "é", "ě", "è", "ī", "í", "ǐ", "ì", "ū", "ú", "ǔ", "ù", "ü", "ǖ", "ǘ", "ǚ", "ǜ", "ḿ", "m̀", "ń", "ň", "ǹ", "ẑ", "ĉ", "ŝ", "ŋ"]
    "/pyd": ["Ā", "Á", "Ǎ", "À", "Ō", "Ó", "Ǒ", "Ò", "Ê", "Ê̄", "Ế", "Ê̌", "Ề", "Ē", "É", "Ě", "È", "Ī", "Í", "Ǐ", "Ì", "Ū", "Ú", "Ǔ", "Ù", "Ü", "Ǖ", "Ǘ", "Ǚ", "Ǜ", "Ḿ", "M̀", "Ń", "Ň", "Ǹ", "Ẑ", "Ĉ", "Ŝ", "Ŋ"]
    "/q": ["ɋ", "ꝗ", "ꝙ", "ʠ"]
    "/qp": ["ȹ"]
    "/r": ["ŕ", "ř", "ȑ", "ȓ", "ṙ", "ᵣ", "ɍ", "ꞧ", "ᵲ", "ŗ", "ṟ", "ṛ", "ṝ", "ᵳ", "ɽ", "ᶉ", "ꭇ", "ꭈ", "ꭊ", "ꭉ", "ꝵ", "ꭋ", "ꭌ", "ɹ", "ʴ", "ɺ", "ɻ", "ʵ", "ⱹ", "ɼ", "ʳ", "ɾ", "ɿ", "ꝛ", "ꝝ"]
    "/rq": ["㏠", "㏡", "㏢", "㏣", "㏤", "㏥", "㏦", "㏧", "㏨", "㏩", "㏪", "㏫", "㏬", "㏭", "㏮", "㏯", "㏰", "㏱", "㏲", "㏳", "㏴", "㏵", "㏶", "㏷", "㏸", "㏹", "㏺", "㏻", "㏼", "㏽", "㏾"]
    "/s": ["/s.", "🆚", "ś", "ŝ", "š", "ṡ", "ˢ", "ʂ", "ᶳ", "ᵴ", "ꞩ", "ᶊ", "ş", "ṣ", "ș", "ȿ", "ṥ", "ṧ", "ṩ", "ʃ", "ᶴ", "ʆ", "ᶘ", "ʅ", "ƪ", "ß", "ſ", "ẛ", "ẜ", "ẝ"]
    "/sb": ["⁰", "¹", "²", "³", "⁴", "⁵", "⁶", "⁷", "⁸", "⁹", "˜", "⁺", "⁻", "⁼", "⁽", "⁾", "ᴬ", "ᵃ", "ᵄ", "ᵅ", "ᶛ", "ᴭ", "ᵆ", "ᴮ", "ᴯ", "ᵇ", "ᵝ", "ᶜ", "ᵓ", "ᶝ", "ᴰ", "ᵈ", "ᶞ", "ᵟ", "ᴱ", "ᵉ", "ᴲ", "ᵊ", "ᵋ", "ᶟ", "ᵌ", "ᶠ", "ᶡ", "ᶲ", "ᵠ", "ᴳ", "ᵍ", "ᶢ", "ˠ", "ᵞ", "ᴴ", "ʰ", "ᶣ", "ʱ", "ᴵ", "ⁱ", "ᶤ", "ᵎ", "ᶥ", "ᴶ", "ʲ", "ᶨ", "ᴷ", "ᵏ", "ᴸ", "ᶫ", "ˡ", "ᶩ", "ᶪ", "ᴹ", "ᵐ", "ᶬ", "ᵚ", "ᶭ", "ᴺ", "ᴻ", "ⁿ", "ᵑ", "ᶮ", "ᶯ", "ᴼ", "ᵒ", "ᶱ", "ᴽ", "ᴾ", "ᵖ", "ᴿ", "ʳ", "ʶ", "ʴ", "ʵ", "ˢ", "ᶴ", "ᶳ", "ᵀ", "ᵗ", "ᶵ", "ᶿ", "ᵁ", "ᵘ", "ᶶ", "ᶷ", "ᵙ", "ⱽ", "ᵛ", "ᶺ", "ᶹ", "ᵂ", "ʷ", "ˣ", "ᵡ", "ʸ", "ᶻ", "ᶾ", "ᶽ", "ᶼ"]
    "/sd": ["ˉ", "ˊ", "ˇ", "ˋ", "ˆ", "˙", "˜", "˥", "˦", "˧", "˨", "˩", "꜀", "꜁", "꜂", "꜃", "꜄", "꜅", "꜆", "꜇", "〪", "〫", "〬", "〭"]
    "/sj": ["㍘", "㍙", "㍚", "㍛", "㍜", "㍝", "㍞", "㍟", "㍠", "㍡", "㍢", "㍣", "㍤", "㍥", "㍦", "㍧", "㍨", "㍩", "㍪", "㍫", "㍬", "㍭", "㍮", "㍯", "㍰"]
    "/sx": ["±", "÷", "×", "∈", "∏", "∑", "－", "＋", "＜", "≮", "＝", "≠", "＞", "≯", "∕", "√", "∝", "∞", "∟", "∠", "∥", "∧", "∨", "∩", "∪", "∫", "∮", "∴", "∵", "∷", "∽", "≈", "≌", "≒", "≡", "≤", "≥", "≦", "≧", "⊕", "⊙", "⊥", "⊿", "㏑", "㏒"]
    "/sz": ["⚀", "⚁", "⚂", "⚃", "⚄", "⚅"]
    "/szd": ["⒈", "⒉", "⒊", "⒋", "⒌", "⒍", "⒎", "⒏", "⒐", "⒑", "⒒", "⒓", "⒔", "⒕", "⒖", "⒗", "⒘", "⒙", "⒚", "⒛"]
    "/szh": ["⑴", "⑵", "⑶", "⑷", "⑸", "⑹", "⑺", "⑻", "⑼", "⑽", "⑾", "⑿", "⒀", "⒁", "⒂", "⒃", "⒄", "⒅", "⒆", "⒇"]
    "/szm": ["〡", "〢", "〣", "〤", "〥", "〦", "〧", "〨", "〩", "〸", "〹", "〺"]
    "/szq": ["⓪", "①", "②", "③", "④", "⑤", "⑥", "⑦", "⑧", "⑨", "⑩", "⑪", "⑫", "⑬", "⑭", "⑮", "⑯", "⑰", "⑱", "⑲", "⑳", "㉑", "㉒", "㉓", "㉔", "㉕", "㉖", "㉗", "㉘", "㉙", "㉚", "㉛", "㉜", "㉝", "㉞", "㉟", "㊱", "㊲", "㊳", "㊴", "㊵", "㊶", "㊷", "㊸", "㊹", "㊺", "㊻", "㊼", "㊽", "㊾", "㊿", "⓿", "❶", "❷", "❸", "❹", "❺", "❻", "❼", "❽", "❾", "❿", "⓫", "⓬", "⓭", "⓮", "⓯", "⓰", "⓱", "⓲", "⓳", "⓴"]
    "/t": ["ť", "ṫ", "ẗ", "ᵗ", "ₜ", "ʈ", "þ", "ꝥ", "ꝧ", "ŧ", "ⱦ", "ţ", "ṯ", "ṱ", "ṭ", "ț", "ƭ", "ᵵ", "ƫ", "ᶵ", "ʇ", "ȶ", "ꝷ"]
    "/tc": ["ʨ"]
    "/tg": ["甲", "乙", "丙", "丁", "戊", "己", "庚", "辛", "壬", "癸"]
    "/th": ["ᵺ"]
    "/tq": ["☀", "☁", "⛅", "⛈", "⛆", "☂", "☔", "☃", "⛄", "⛇"]
    "/ts": ["ʦ", "ʧ"]
    "/tt": ["☄", "☼", "☽", "☾", "☿", "♀", "♁", "♂", "♃", "♄", "♅", "♆", "♇"]
    "/txj": ["⚊", "⚋", "⚌", "⚍", "⚎", "⚏", "𝌀", "𝌁", "𝌂", "𝌃", "𝌄", "𝌅", "𝌆", "𝌇", "𝌈", "𝌉", "𝌊", "𝌋", "𝌌", "𝌍", "𝌎", "𝌏", "𝌐", "𝌑", "𝌒", "𝌓", "𝌔", "𝌕", "𝌖", "𝌗", "𝌘", "𝌙", "𝌚", "𝌛", "𝌜", "𝌝", "𝌞", "𝌟", "𝌠", "𝌡", "𝌢", "𝌣", "𝌤", "𝌥", "𝌦", "𝌧", "𝌨", "𝌩", "𝌪", "𝌫", "𝌬", "𝌭", "𝌮", "𝌯", "𝌰", "𝌱", "𝌲", "𝌳", "𝌴", "𝌵", "𝌶", "𝌷", "𝌸", "𝌹", "𝌺", "𝌻", "𝌼", "𝌽", "𝌾", "𝌿", "𝍀", "𝍁", "𝍂", "𝍃", "𝍄", "𝍅", "𝍆", "𝍇", "𝍈", "𝍉", "𝍊", "𝍋", "𝍌", "𝍍", "𝍎", "𝍏", "𝍐", "𝍑", "𝍒", "𝍓", "𝍔", "𝍕", "𝍖"]
    "/tz": ["ꜩ"]
    "/u": ["ū", "ú", "ǔ", "ù", "ű", "ȕ", "û", "ŭ", "ȗ", "ü", "ǖ", "ǘ", "ǚ", "ǜ", "ů", "ũ", "ᵤ", "ᵘ", "ʉ", "ᶶ", "ủ", "ų", "ṷ", "ụ", "ṳ", "ṵ", "ư", "ʊ", "ᶷ", "ᵿ", "ᶙ", "ṻ", "ṹ", "ứ", "ừ", "ữ", "ử", "ự", "ꭒ", "ꭟ", "ꝸ", "ꭎ", "ꭏ", "ᴝ", "ᵙ", "ᴞ"]
    "/ue": ["ᵫ"]
    "/v": ["ü", "ǖ", "ǘ", "ǚ", "ǜ", "ṽ", "ᵛ", "ᵥ", "ṿ", "ꝟ", "ʋ", "ᶹ", "ᶌ", "ⱴ", "ⱱ", "ỽ", "ʌ", "ᶺ"]
    "/vy": ["ꝡ"]
    "/w": ["ẃ", "ẁ", "ŵ", "ẇ", "ẅ", "ẘ", "ʷ", "ẉ", "ƿ", "ʍ", "ⱳ"]
    "/ww": ["ʬ"]
    "/x": ["ẋ", "ẍ", "ᶍ", "ˣ", "ₓ", "ꭖ", "ꭗ", "ꭘ", "ꭙ"]
    "/xb": ["₀", "₁", "₂", "₃", "₄", "₅", "₆", "₇", "₈", "₉", "₊", "₋", "₌", "₍", "₎", "‸", "ᴀ", "ₐ", "ᴁ", "ʙ", "ᴃ", "ᵦ", "ᴄ", "ᴐ", "ᴒ", "ᴅ", "ᴆ", "ᴇ", "ₑ", "ₔ", "ᵩ", "ɢ", "ʛ", "ᴦ", "ᵧ", "ʜ", "ₕ", "ɪ", "ᵻ", "ᵢ", "ᴊ", "ⱼ", "ᴋ", "ₖ", "ʟ", "ₗ", "ᴌ", "ᴧ", "ᴍ", "ₘ", "ꟺ", "ɴ", "ᴎ", "ₙ", "ᴏ", "ₒ", "ɶ", "ʘ", "ᴓ", "ᴑ", "ᴘ", "ₚ", "ᴨ", "ᴪ", "ʀ", "ᵣ", "ᴙ", "ʁ", "ᴚ", "ᵨ", "ₛ", "ᴛ", "ₜ", "ᴜ", "ᵤ", "ᵾ", "ᴠ", "ᵥ", "ᴡ", "ₓ", "ᵪ", "ʏ", "ᴢ", "ᴣ"]
    "/xh": ["★", "☆", "⛤", "⛥", "⛦", "⛧", "✡", "❋", "❊", "❉", "❈", "❇", "❆", "❅", "❄", "❃", "❂", "❁", "❀", "✿", "✾", "✽", "✼", "✻", "✺", "✹", "✸", "✷", "✶", "✵", "✴", "✳", "✲", "✱", "✰", "✯", "✮", "✭", "✬", "✫", "✪", "✩", "✧", "✦", "✥", "✤", "✣", "✢"]
    "/xl": ["α", "β", "γ", "δ", "ε", "ζ", "η", "θ", "ι", "κ", "λ", "μ", "ν", "ξ", "ο", "π", "ρ", "σ", "τ", "υ", "φ", "χ", "ψ", "ω"]
    "/xld": ["Α", "Β", "Γ", "Δ", "Ε", "Ζ", "Η", "Θ", "Ι", "Κ", "Λ", "Μ", "Ν", "Ξ", "Ο", "Π", "Ρ", "Σ", "Τ", "Υ", "Φ", "Χ", "Ψ", "Ω"]
    "/xq": ["♔", "♕", "♖", "♗", "♘", "♙", "♚", "♛", "♜", "♝", "♞", "♟"]
    "/xz": ["♈", "♉", "♊", "♋", "♌", "♍", "♎", "♏", "♐", "♑", "♒", "♓"]
    "/xzg": ["白羊宫", "金牛宫", "双子宫", "巨蟹宫", "狮子宫", "室女宫", "天秤宫", "天蝎宫", "人马宫", "摩羯宫", "宝瓶宫", "双鱼宫"]
    "/xzm": ["白羊座", "金牛座", "双子座", "巨蟹座", "狮子座", "室女座", "天秤座", "天蝎座", "人马座", "摩羯座", "宝瓶座", "双鱼座"]
    "/y": ["ȳ", "ý", "ỳ", "ŷ", "ẏ", "ÿ", "ẙ", "ỹ", "ʸ", "ɏ", "ỷ", "ỵ", "ƴ", "ʎ", "ỿ", "ꭚ"]
    "/yf": ["㋀", "㋁", "㋂", "㋃", "㋄", "㋅", "㋆", "㋇", "㋈", "㋉", "㋊", "㋋"]
    "/yr": ["月", "火", "水", "木", "金", "土", "日", "㊊", "㊋", "㊌", "㊍", "㊎", "㊏", "㊐", "㊗", "㊡", "㈪", "㈫", "㈬", "㈭", "㈮", "㈯", "㈰", "㈷", "㉁", "㉀"]
    "/yy": ["𝄞", "♩", "♪", "♫", "♬", "♭", "♮", "♯"]
    "/z": ["ź", "ž", "ẑ", "ż", "ᶻ", "ʐ", "ᶼ", "ʑ", "ᶽ", "ƶ", "ẕ", "ẓ", "ᵶ", "ȥ", "ⱬ", "ᶎ", "ʒ", "ᶾ", "ǯ", "ʓ", "ƹ", "ƺ", "ᶚ", "θ", "ᶿ", "ɀ", "ꝣ"]
    "/zmh": ["⒜", "⒝", "⒞", "⒟", "⒠", "⒡", "⒢", "⒣", "⒤", "⒥", "⒦", "⒧", "⒨", "⒩", "⒪", "⒫", "⒬", "⒭", "⒮", "⒯", "⒰", "⒱", "⒲", "⒳", "⒴", "⒵"]
    "/zmq": ["ⓐ", "Ⓐ", "ⓑ", "Ⓑ", "ⓒ", "Ⓒ", "ⓓ", "Ⓓ", "ⓔ", "Ⓔ", "ⓕ", "Ⓕ", "ⓖ", "Ⓖ", "ⓗ", "Ⓗ", "ⓘ", "Ⓘ", "ⓙ", "Ⓙ", "ⓚ", "Ⓚ", "ⓛ", "Ⓛ", "ⓜ", "Ⓜ", "ⓝ", "Ⓝ", "ⓞ", "Ⓞ", "ⓟ", "Ⓟ", "ⓠ", "Ⓠ", "ⓡ", "Ⓡ", "ⓢ", "Ⓢ", "ⓣ", "Ⓣ", "ⓤ", "Ⓤ", "ⓥ", "Ⓥ", "ⓦ", "Ⓦ", "ⓧ", "Ⓧ", "ⓨ", "Ⓨ", "ⓩ", "Ⓩ"]
    "/zy": ["ㄅ", "ㄆ", "ㄇ", "ㄈ", "ㄉ", "ㄊ", "ㄋ", "ㄌ", "ㄍ", "ㄎ", "ㄏ", "ㄐ", "ㄑ", "ㄒ", "ㄓ", "ㄔ", "ㄕ", "ㄖ", "ㄗ", "ㄘ", "ㄙ", "ㄧ", "ㄨ", "ㄩ", "ㄚ", "ㄛ", "ㄜ", "ㄝ", "ㄞ", "ㄟ", "ㄠ", "ㄡ", "ㄢ", "ㄣ", "ㄤ", "ㄥ", "ㄦ", "ㄪ", "ㄫ", "ㄬ", "ㄭ", "ㆠ", "ㆡ", "ㆢ", "ㆣ", "ㆤ", "ㆥ", "ㆦ", "ㆧ", "ㆨ", "ㆩ", "ㆪ", "ㆫ", "ㆬ", "ㆭ", "ㆮ", "ㆯ", "ㆰ", "ㆱ", "ㆲ", "ㆳ", "ㆴ", "ㆵ", "ㆶ", "ㆷ"]
quick_symbol_text:
  0: "⓪"
  1: "①"
  2: "②"
  3: "③"
  4: "④"
  5: "⑤"
  6: "⑥"
  7: "⑦"
  8: "⑧"
  9: "⑨"
  a: "！"
  b: "%"
  c: "！”"
  d: "、"
  e: "（"
  f: "“"
  g: "”"
  h: "‘"
  i: "』"
  j: "’"
  k: "【"
  l: "】"
  m: "》"
  n: "《"
  o: "〖"
  p: "〗"
  q: "‰"
  r: "）"
  s: "……"
  t: "~"
  u: "『"
  v: "——"
  w: "？"
  x: "？”"
  y: "·"
  z: "。”"
radical_lookup:
  dictionary: wanxiang_radical
  enable_user_dict: false
  extra_tags:
    - reverse_stroke
  prefix: "`"
  tag: radical_lookup
  tips: "〔反查：部件|笔画〕"
radical_reverse_lookup:
  dictionary: wanxiang
  overwrite_comment: true
  tags:
    - radical_lookup
    - reverse_stroke
recognizer:
  import_preset: default
  patterns:
    add_user_dict: "^``[A-Za-z/`']*$"
    calculator: "^V.*$"
    email: "^[A-Za-z][-_.0-9A-Za-z]*@.*$"
    gregorian_to_lunar: "^N[0-9]{1,8}"
    number: "^R[0-9]+[.]?[0-9]*"
    punct: "^/([0-9]|10|[A-Za-z]+)$"
    quick_symbol: "^;.*$"
    radical_lookup: "^`[A-Za-z]*$"
    unicode: "^U[a-f0-9]+"
    uppercase: "[A-Z][-_+.'0-9A-Za-z]*$"
    url: "^(www[.]|https?:|ftp[.:]|mailto:|file:).*$|^[a-z]+[.].+$"
reverse_stroke:
  db_class: stabledb
  dictionary: wanxiang_stroke
  enable_completion: true
  preedit_format:
    - "xlit/hspnz/一丨丿丶乙/"
  suffix: "'"
  tag: reverse_stroke
s2hk:
  opencc_config: s2hk.json
  option_name: s2hk
  tags:
    - abc
s2t:
  opencc_config: s2t.json
  option_name: s2t
  tags:
    - abc
  tips: none
s2tw:
  opencc_config: s2tw.json
  option_name: s2tw
  tags:
    - abc
schema:
  ascii_icon: "icons/ying.ico"
  icon: "icons/zhong.ico"
set_algebra_fuzhu:
  __append:
    - "xform|^(.*?);(.*?);.*$|$1;$2|"
set_chord_composer:
  chord_composer:
    algebra: ["xlit|;,./|ACXZ|", "xform/([qwertasdfgzxcvb]+)/<$1>/", "xform/([yuiophjklAnmCXZ]+)/<$1>/", "xform=(<q>|<p>)=q=", "xform=(<w>|<o>)=w=", "xform=(<e>|<i>)=e=", "xform=(<r>|<u>)=r=", "xform=(<t>|<y>)=t=", "xform=(<ef>|<ji>)=y=", "xform=(<er>|<ui>)=u=", "xform=(<we>|<io>)=i=", "xform=(<wr>|<uo>)=o=", "xform=(<qr>|<up>)=p=", "xform=(<a>|<A>)=a=", "xform=(<s>|<l>)=s=", "xform=(<d>|<k>)=d=", "xform=(<f>|<j>)=f=", "xform=(<g>|<h>)=g=", "xform=(<se>|<il>)=h=", "xform=(<wf>|<jo>)=h=", "xform=(<df>|<jk>)=j=", "xform=(<sd>|<kl>)=k=", "xform=(<sf>|<jl>)=l=", "xform=(<z>|<Z>)=z=", "xform=(<x>|<X>)=x=", "xform=(<c>|<C>)=c=", "xform=(<v>|<m>)=v=", "xform=(<b>|<n>)=b=", "xform=(<af>|<jA>)=n=", "xform=(<cv>|<mC>)=m=", "xform=(<dg>)=,=", "xform=(<ag>)=.=", "xform=(<hk>)=!=", "xform=(<hA>)=?=", "xform=(<xc>|<CX>)=,=", "xform=(<xv>|<mX>)=.=", "xform=(<zx>|<XZ>)=!=", "xform=(<zv>|<mZ>)=?=", "xform=(<ad>|<kA>)=;=", "xform=(<as>|<lA>)=/=", "xform=(<vb>|<nm>)=/=", "xform=(<rt>|<yu>)=“=", "xform=(<et>|<yi>)=”=", "xform=(<qa>|<pA>)=~”=", "xform=(<aw>|<oA>)=^”=", "xform=(<ed>|<ik>)=!”=", "xform=(<rf>|<uj>)=?”=", "xform=(<ar>|<uA>)=:“=", "xform=(<sr>|<ul>)=.”=", "xform=(<qw>|<op>)=,“=", "xform=(<zf>|<jZ>)=+=", "xform=(<xf>|<jX>)=-=", "xform=(<cf>|<jC>)=%=", "xform=(<dr>|<uk>)=*=", "xform=(<qe>|<ip>)=@=", "xform=(<tg>)=:=", "xform=(<yh>)=#=", "xform=(<fg>)=~=", "xform=(<hj>)=^=", "xform/<1>/ /", "xform/<\"1>/\"/", "xform/^.*<.+>.*$//", "xform=，=\"\"\"\",=", "xform=。=\"\"\"\".=", "xform=！=\"\"\"\"!=", "xform=？=\"\"\"\"?="]
    alphabet: "qazwsxedcrfvtgbyhnujmik,ol.p;/ `"
    finish_chord_on_first_key_release: true
set_cn_en:
  user_dict: "en_dicts/flypy"
set_fuzhu_type:
  __append:
    - "derive/^(.*?)(\\d?);.*$/$1/"
    - "derive/^(.*?)(\\d?);.*$/$1$2/"
    - "derive/^(.*?)(\\d?);(\\w).*$/$1$3/"
    - "derive|^(.*?)(\\d?);(\\w).*$|$1$2$3|"
    - "derive|^(.*?)(\\d?);(\\w).*$|$1$3$2|"
    - "abbrev/^(.*?)(\\d?);(\\w)(\\w).*$/$1$3$4/"
    - "derive|^(.*?)(\\d?);(\\w)(\\w).*$|$1$3$4$2|"
    - "derive|^(.*?)(\\d?);(\\w)(\\w).*$|$1$2$3$4|"
    - "derive|^(.*?)(\\d?);(\\w)(\\w).*$|$1$3$2$4|"
    - "derive|^(.*?)(\\d?);(\\w)(\\w).*$|$1$3$4/|"
    - "derive|^(.*?)(\\d?);(\\w)(\\w).*$|$1$3$4o|"
    - "derive/^(.*?)(\\d?);.*?,(\\w).*$/$1$3/"
    - "derive|^(.*?)(\\d?);.*?,(\\w).*$|$1$2$3|"
    - "derive|^(.*?)(\\d?);.*?,(\\w).*$|$1$3$2|"
    - "abbrev/^(.*?)(\\d?);.*?,(\\w)(\\w).*$/$1$3$4/"
    - "derive|^(.*?)(\\d?);.*?,(\\w)(\\w).*$|$1$3$4$2|"
    - "derive|^(.*?)(\\d?);.*?,(\\w)(\\w).*$|$1$2$3$4|"
    - "derive|^(.*?)(\\d?);.*?,(\\w)(\\w).*$|$1$3$2$4|"
    - "derive|^(.*?)(\\d?);.*?,(\\w)(\\w).*$|$1$3$4/|"
    - "derive|^(.*?)(\\d?);.*?,(\\w)(\\w).*$|$1$3$4o|"
    - "derive/^(.*?)(\\d?);.*?,.*?,(\\w).*$/$1$3/"
    - "derive|^(.*?)(\\d?);.*?,.*?,(\\w).*$|$1$2$3|"
    - "derive|^(.*?)(\\d?);.*?,.*?,(\\w).*$|$1$3$2|"
    - "abbrev/^(.*?)(\\d?);.*?,.*?,(\\w)(\\w).*$/$1$3$4/"
    - "derive|^(.*?)(\\d?);.*?,.*?,(\\w)(\\w).*$|$1$3$4$2|"
    - "derive|^(.*?)(\\d?);.*?,.*?,(\\w)(\\w).*$|$1$2$3$4|"
    - "derive|^(.*?)(\\d?);.*?,.*?,(\\w)(\\w).*$|$1$3$2$4|"
    - "derive|^(.*?)(\\d?);.*?,.*?,(\\w)(\\w).*$|$1$3$4/|"
    - "derive|^(.*?)(\\d?);.*?,.*?,(\\w)(\\w).*$|$1$3$4o|"
    - "derive/^(.*?)(\\d?);.*?,.*?,.*?,(\\w).*$/$1$3/"
    - "derive|^(.*?)(\\d?);.*?,.*?,.*?,(\\w).*$|$1$2$3|"
    - "derive|^(.*?)(\\d?);.*?,.*?,.*?,(\\w).*$|$1$3$2|"
    - "abbrev/^(.*?)(\\d?);.*?,.*?,.*?,(\\w)(\\w).*$/$1$3$4/"
    - "derive|^(.*?)(\\d?);.*?,.*?,.*?,(\\w)(\\w).*$|$1$3$4$2|"
    - "derive|^(.*?)(\\d?);.*?,.*?,.*?,(\\w)(\\w).*$|$1$2$3$4|"
    - "derive|^(.*?)(\\d?);.*?,.*?,.*?,(\\w)(\\w).*$|$1$3$2$4|"
    - "derive|^(.*?)(\\d?);.*?,.*?,.*?,(\\w)(\\w).*$|$1$3$4/|"
    - "derive|^(.*?)(\\d?);.*?,.*?,.*?,(\\w)(\\w).*$|$1$3$4o|"
    - "xform/◯/;/"
    - "erase/^(.+);(.+)$/"
set_shuru_schema:
  __append:
    - "xform/^([a-z]+)(;.*?)$/$1④$2/"
    - "xform/^(.*)ā(.*?)(;.*)$/$1a$2①$3/"
    - "xform/^(.*)á(.*?)(;.*)$/$1a$2②$3/"
    - "xform/^(.*)ǎ(.*?)(;.*)$/$1a$2③$3/"
    - "xform/^(.*)à(.*?)(;.*)$/$1a$2④$3/"
    - "xform/^(.*)ō(.*?)(;.*)$/$1o$2①$3/"
    - "xform/^(.*)ó(.*?)(;.*)$/$1o$2②$3/"
    - "xform/^(.*)ǒ(.*?)(;.*)$/$1o$2③$3/"
    - "xform/^(.*)ò(.*?)(;.*)$/$1o$2④$3/"
    - "xform/^(.*)ē(.*?)(;.*)$/$1e$2①$3/"
    - "xform/^(.*)é(.*?)(;.*)$/$1e$2②$3/"
    - "xform/^(.*)ě(.*?)(;.*)$/$1e$2③$3/"
    - "xform/^(.*)è(.*?)(;.*)$/$1e$2④$3/"
    - "xform/^(.*)ī(.*?)(;.*)$/$1i$2①$3/"
    - "xform/^(.*)í(.*?)(;.*)$/$1i$2②$3/"
    - "xform/^(.*)ǐ(.*?)(;.*)$/$1i$2③$3/"
    - "xform/^(.*)ì(.*?)(;.*)$/$1i$2④$3/"
    - "xform/^(.*)ū(.*?)(;.*)$/$1u$2①$3/"
    - "xform/^(.*)ú(.*?)(;.*)$/$1u$2②$3/"
    - "xform/^(.*)ǔ(.*?)(;.*)$/$1u$2③$3/"
    - "xform/^(.*)ù(.*?)(;.*)$/$1u$2④$3/"
    - "xform/^(.*)ǖ(.*?)(;.*)$/$1v$2①$3/"
    - "xform/^(.*)ǘ(.*?)(;.*)$/$1v$2②$3/"
    - "xform/^(.*)ǚ(.*?)(;.*)$/$1v$2③$3/"
    - "xform/^(.*)ǜ(.*?)(;.*)$/$1v$2④$3/"
    - "xform/^(.*)ü(.*?)$/$1v$2/"
    - "xform/^(.*)ń(.*?)(;.*)$/$1n$2②$3/"
    - "xform/^(.*)ň(.*?)(;.*)$/$1n$2③$3/"
    - "xform/^(.*)ǹ(.*?)(;.*)$/$1n$2④$3/"
    - "xlit/①②③④/7890"
    - "derive/^ng(\\d)(;.*)$/eng$1$2/"
    - "xform/^n(\\d)(;.*)/en$1$2/"
    - "derive/^([jqxy])u(\\d)(;.*)$/$1v$2$3/"
    - "derive/^([aoe])([ioun])(\\d)(;.*)$/$1$1$2$3$4/"
    - "xform/^([aoe])(ng)?(\\d)(;.*)$/$1$1$2$3$4/"
    - "xform/^(\\w+?)iu(\\d)(;.*)$/$1Ⓠ$2$3/"
    - "xform/^(\\w+?)[uv]an(\\d)(;.*)$/$1Ⓡ$2$3/"
    - "xform/^(\\w+?)[uv]e(\\d)(;.*)$/$1Ⓣ$2$3/"
    - "xform/^(\\w+?)ing(\\d)(;.*)$/$1Ⓨ$2$3/"
    - "xform/^(\\w+?)uai(\\d)(;.*)$/$1Ⓨ$2$3/"
    - "xform/^(\\w+?)uo(\\d)(;.*)$/$1Ⓞ$2$3/"
    - "xform/^(\\w+?)[uv]n(\\d)(;.*)$/$1Ⓟ$2$3/"
    - "xform/^(\\w+?)i?ong(\\d)(;.*)$/$1Ⓢ$2$3/"
    - "xform/^(\\w+?)[iu]ang(\\d)(;.*)$/$1Ⓓ$2$3/"
    - "xform/^(\\w+?)en(\\d)(;.*)$/$1Ⓕ$2$3/"
    - "xform/^(\\w+?)eng(\\d)(;.*)$/$1Ⓖ$2$3/"
    - "xform/^(\\w+?)ang(\\d)(;.*)$/$1Ⓗ$2$3/"
    - "xform/^(\\w+?)ian(\\d)(;.*)$/$1Ⓜ$2$3/"
    - "xform/^(\\w+?)an(\\d)(;.*)$/$1Ⓙ$2$3/"
    - "xform/^(\\w+?)iao(\\d)(;.*)$/$1Ⓒ$2$3/"
    - "xform/^(\\w+?)ao(\\d)(;.*)$/$1Ⓚ$2$3/"
    - "xform/^(\\w+?)ai(\\d)(;.*)$/$1Ⓛ$2$3/"
    - "xform/^(\\w+?)ei(\\d)(;.*)$/$1Ⓩ$2$3/"
    - "xform/^(\\w+?)ie(\\d)(;.*)$/$1Ⓧ$2$3/"
    - "xform/^(\\w+?)ui(\\d)(;.*)$/$1Ⓥ$2$3/"
    - "xform/^(\\w+?)ou(\\d)(;.*)$/$1Ⓑ$2$3/"
    - "xform/^(\\w+?)in(\\d)(;.*)$/$1Ⓝ$2$3/"
    - "xform/^(\\w+?)[iu]a(\\d)(;.*)$/$1Ⓦ$2$3/"
    - "xform/^sh/Ⓤ/"
    - "xform/^ch/Ⓘ/"
    - "xform/^zh/Ⓥ/"
    - "xlit/ⓆⓌⓇⓉⓎⓊⒾⓄⓅⓈⒹⒻⒼⒽⓂⒿⒸⓀⓁⓏⓍⓋⒷⓃ/qwrtyuiopsdfghmjcklzxvbn/"
speller:
  algebra:
    - "xform/^([a-z]+)(;.*?)$/$1④$2/"
    - "xform/^(.*)ā(.*?)(;.*)$/$1a$2①$3/"
    - "xform/^(.*)á(.*?)(;.*)$/$1a$2②$3/"
    - "xform/^(.*)ǎ(.*?)(;.*)$/$1a$2③$3/"
    - "xform/^(.*)à(.*?)(;.*)$/$1a$2④$3/"
    - "xform/^(.*)ō(.*?)(;.*)$/$1o$2①$3/"
    - "xform/^(.*)ó(.*?)(;.*)$/$1o$2②$3/"
    - "xform/^(.*)ǒ(.*?)(;.*)$/$1o$2③$3/"
    - "xform/^(.*)ò(.*?)(;.*)$/$1o$2④$3/"
    - "xform/^(.*)ē(.*?)(;.*)$/$1e$2①$3/"
    - "xform/^(.*)é(.*?)(;.*)$/$1e$2②$3/"
    - "xform/^(.*)ě(.*?)(;.*)$/$1e$2③$3/"
    - "xform/^(.*)è(.*?)(;.*)$/$1e$2④$3/"
    - "xform/^(.*)ī(.*?)(;.*)$/$1i$2①$3/"
    - "xform/^(.*)í(.*?)(;.*)$/$1i$2②$3/"
    - "xform/^(.*)ǐ(.*?)(;.*)$/$1i$2③$3/"
    - "xform/^(.*)ì(.*?)(;.*)$/$1i$2④$3/"
    - "xform/^(.*)ū(.*?)(;.*)$/$1u$2①$3/"
    - "xform/^(.*)ú(.*?)(;.*)$/$1u$2②$3/"
    - "xform/^(.*)ǔ(.*?)(;.*)$/$1u$2③$3/"
    - "xform/^(.*)ù(.*?)(;.*)$/$1u$2④$3/"
    - "xform/^(.*)ǖ(.*?)(;.*)$/$1v$2①$3/"
    - "xform/^(.*)ǘ(.*?)(;.*)$/$1v$2②$3/"
    - "xform/^(.*)ǚ(.*?)(;.*)$/$1v$2③$3/"
    - "xform/^(.*)ǜ(.*?)(;.*)$/$1v$2④$3/"
    - "xform/^(.*)ü(.*?)$/$1v$2/"
    - "xform/^(.*)ń(.*?)(;.*)$/$1n$2②$3/"
    - "xform/^(.*)ň(.*?)(;.*)$/$1n$2③$3/"
    - "xform/^(.*)ǹ(.*?)(;.*)$/$1n$2④$3/"
    - "xlit/①②③④/7890"
    - "derive/^ng(\\d)(;.*)$/eng$1$2/"
    - "xform/^n(\\d)(;.*)/en$1$2/"
    - "derive/^([jqxy])u(\\d)(;.*)$/$1v$2$3/"
    - "derive/^([aoe])([ioun])(\\d)(;.*)$/$1$1$2$3$4/"
    - "xform/^([aoe])(ng)?(\\d)(;.*)$/$1$1$2$3$4/"
    - "xform/^(\\w+?)iu(\\d)(;.*)/$1Ⓠ$2$3/"
    - "xform/^(\\w+?)ei(\\d)(;.*)/$1Ⓦ$2$3/"
    - "xform/^(\\w+?)uan(\\d)(;.*)/$1Ⓡ$2$3/"
    - "xform/^(\\w+?)[uv]e(\\d)(;.*)/$1Ⓣ$2$3/"
    - "xform/^(\\w+?)un(\\d)(;.*)/$1Ⓨ$2$3/"
    - "xform/^(\\w+?)uo(\\d)(;.*)/$1Ⓞ$2$3/"
    - "xform/^(\\w+?)ie(\\d)(;.*)/$1Ⓟ$2$3/"
    - "xform/^(\\w+?)i?ong(\\d)(;.*)/$1Ⓢ$2$3/"
    - "xform/^(\\w+?)ing(\\d)(;.*)/$1Ⓚ$2$3/"
    - "xform/^(\\w+?)uai(\\d)(;.*)/$1Ⓚ$2$3/"
    - "xform/^(\\w+?)ai(\\d)(;.*)/$1Ⓓ$2$3/"
    - "xform/^(\\w+?)eng(\\d)(;.*)/$1Ⓖ$2$3/"
    - "xform/^(\\w+?)en(\\d)(;.*)/$1Ⓕ$2$3/"
    - "xform/^(\\w+?)[iu]ang(\\d)(;.*)/$1Ⓛ$2$3/"
    - "xform/^(\\w+?)ang(\\d)(;.*)/$1Ⓗ$2$3/"
    - "xform/^(\\w+?)ian(\\d)(;.*)/$1Ⓜ$2$3/"
    - "xform/^(\\w+?)an(\\d)(;.*)/$1Ⓙ$2$3/"
    - "xform/^(\\w+?)ou(\\d)(;.*)/$1Ⓩ$2$3/"
    - "xform/^(\\w+?)iao(\\d)(;.*)/$1Ⓝ$2$3/"
    - "xform/^(\\w+?)[iu]a(\\d)(;.*)/$1Ⓧ$2$3/"
    - "xform/^(\\w+?)ao(\\d)(;.*)/$1Ⓒ$2$3/"
    - "xform/^(\\w+?)ui(\\d)(;.*)/$1Ⓥ$2$3/"
    - "xform/^(\\w+?)in(\\d)(;.*)/$1Ⓑ$2$3/"
    - "xform/^sh/Ⓤ/"
    - "xform/^ch/Ⓘ/"
    - "xform/^zh/Ⓥ/"
    - "xlit/ⓆⓌⓇⓉⓎⓊⒾⓄⓅⓈⒹⒻⒼⒽⒿⓀⓁⓏⓍⒸⓋⒷⓃⓂ/qwrtyuiopsdfghjklzxcvbnm/"
    - "derive/^(.*?)(\\d?);.*$/$1/"
    - "derive/^(.*?)(\\d?);.*$/$1$2/"
    - "derive|^(.*?)(\\d?);(\\w).*$|$1/$3|"
    - "derive|^(.*?)(\\d?);(\\w).*$|$1$2/$3|"
    - "derive|^(.*?)(\\d?);(\\w)(\\w).*$|$1/$3$4|"
    - "derive|^(.*?)(\\d?);(\\w)(\\w).*$|$1$2/$3$4|"
    - "derive|^(.*?)(\\d?);.*?,(\\w).*$|$1/$3|"
    - "derive|^(.*?)(\\d?);.*?,(\\w).*$|$1$2/$3|"
    - "derive|^(.*?)(\\d?);.*?,(\\w)(\\w).*$|$1/$3$4|"
    - "derive|^(.*?)(\\d?);.*?,(\\w)(\\w).*$|$1$2/$3$4|"
    - "derive|^(.*?)(\\d?);.*?,.*?,(\\w).*$|$1/$3|"
    - "derive|^(.*?)(\\d?);.*?,.*?,(\\w).*$|$1$2/$3|"
    - "derive|^(.*?)(\\d?);.*?,.*?,(\\w)(\\w).*$|$1/$3$4|"
    - "derive|^(.*?)(\\d?);.*?,.*?,(\\w)(\\w).*$|$1$2/$3$4|"
    - "derive|^(.*?)(\\d?);.*?,.*?,.*?,(\\w).*$|$1/$3|"
    - "derive|^(.*?)(\\d?);.*?,.*?,.*?,(\\w).*$|$1$2/$3|"
    - "derive|^(.*?)(\\d?);.*?,.*?,.*?,(\\w)(\\w).*$|$1/$3$4|"
    - "derive|^(.*?)(\\d?);.*?,.*?,.*?,(\\w)(\\w).*$|$1$2/$3$4|"
    - "xform/◯/;/"
    - "erase/^(.+);(.+)$/"
  alphabet: "zyxwvutsrqponmlkjihgfedcbaZYXWVUTSRQPONMLKJIHGFEDCBA7890`;/"
  delimiter: " '"
  initials: "zyxwvutsrqponmlkjihgfedcbaZYXWVUTSRQPONMLKJIHGFEDCBA/"
super_comment:
  candidate_length: 1
  corrector_type: "{comment}"
  fuzhu_type: zrm
switches:
  - name: ascii_mode
    states: ["中", "英"]
  - name: ascii_punct
    states: ["¥", "$"]
  - options: [s2s, s2t, s2hk, s2tw]
    states: ["简体", "通繁", "港繁", "臺繁"]
  - name: full_shape
    states: ["半", "全"]
  - name: emoji
    states: ["🙃", "😄"]
  - name: chinese_english
    states: ["译", "翻"]
  - name: tone_display
    states: ["调", "声"]
  - name: fuzhu_switch
    states: ["助", "辅"]
  - name: chaifen_switch
    states: ["分", "拆"]
  - name: charset_filter
    states: ["小", "大"]
  - name: super_tips
    states: [off, tips]
  - name: prediction
    states: ["测", "预"]
  - abbrev: ["词", "单"]
    name: search_single_char
    states: ["正常", "单字"]
translator:
  always_show_comments: true
  comment_format:
    {}
  contextual_suggestions: false
  dictionary: wanxiang
  disable_user_dict_for_patterns: "^[a-z]{1,6}"
  enable_completion: true
  enable_correction: false
  enable_user_dict: false
  initial_quality: 3
  max_homographs: 5
  max_homophones: 5
  packs:
    - userxx
  preedit_format:
    - "xform/7/1/"
    - "xform/8/2/"
    - "xform/9/3/"
    - "xform/0/4/"
  spelling_hints: 50
user_dict_set:
  dictionary: wanxiang
  enable_completion: false
  enable_sentence: false
  enable_user_dict: true
  initial_quality: 0
  preedit_format:
    - "xform/7/1/"
    - "xform/8/2/"
    - "xform/9/3/"
    - "xform/0/4/"
  spelling_hints: 100
  user_dict: zc
wanxiang_en:
  comment_format:
    - "xform/.*//"
  dictionary: wanxiang_en
  enable_sentence: false
  enable_user_dict: false
  initial_quality: 1.1
"全拼":
  __append:
    - "xform/^([a-z]+)(;.*?)$/$1④$2/"
    - "xform/^(.*)ā(.*?)(;.*)$/$1a$2①$3/"
    - "xform/^(.*)á(.*?)(;.*)$/$1a$2②$3/"
    - "xform/^(.*)ǎ(.*?)(;.*)$/$1a$2③$3/"
    - "xform/^(.*)à(.*?)(;.*)$/$1a$2④$3/"
    - "xform/^(.*)ō(.*?)(;.*)$/$1o$2①$3/"
    - "xform/^(.*)ó(.*?)(;.*)$/$1o$2②$3/"
    - "xform/^(.*)ǒ(.*?)(;.*)$/$1o$2③$3/"
    - "xform/^(.*)ò(.*?)(;.*)$/$1o$2④$3/"
    - "xform/^(.*)ē(.*?)(;.*)$/$1e$2①$3/"
    - "xform/^(.*)é(.*?)(;.*)$/$1e$2②$3/"
    - "xform/^(.*)ě(.*?)(;.*)$/$1e$2③$3/"
    - "xform/^(.*)è(.*?)(;.*)$/$1e$2④$3/"
    - "xform/^(.*)ī(.*?)(;.*)$/$1i$2①$3/"
    - "xform/^(.*)í(.*?)(;.*)$/$1i$2②$3/"
    - "xform/^(.*)ǐ(.*?)(;.*)$/$1i$2③$3/"
    - "xform/^(.*)ì(.*?)(;.*)$/$1i$2④$3/"
    - "xform/^(.*)ū(.*?)(;.*)$/$1u$2①$3/"
    - "xform/^(.*)ú(.*?)(;.*)$/$1u$2②$3/"
    - "xform/^(.*)ǔ(.*?)(;.*)$/$1u$2③$3/"
    - "xform/^(.*)ù(.*?)(;.*)$/$1u$2④$3/"
    - "xform/^(.*)ǖ(.*?)(;.*)$/$1v$2①$3/"
    - "xform/^(.*)ǘ(.*?)(;.*)$/$1v$2②$3/"
    - "xform/^(.*)ǚ(.*?)(;.*)$/$1v$2③$3/"
    - "xform/^(.*)ǜ(.*?)(;.*)$/$1v$2④$3/"
    - "xform/^(.*)ü(.*?)$/$1v$2/"
    - "xform/^(.*)ń(.*?)(;.*)$/$1n$2②$3/"
    - "xform/^(.*)ň(.*?)(;.*)$/$1n$2③$3/"
    - "xform/^(.*)ǹ(.*?)(;.*)$/$1n$2④$3/"
    - "xlit/①②③④/7890"
    - "derive/^ng(\\d)(;.*)$/eng$1$2/"
    - "xform/^n(\\d)(;.*)/en$1$2/"
"国标双拼":
  __append:
    - "xform/^([a-z]+)(;.*?)$/$1④$2/"
    - "xform/^(.*)ā(.*?)(;.*)$/$1a$2①$3/"
    - "xform/^(.*)á(.*?)(;.*)$/$1a$2②$3/"
    - "xform/^(.*)ǎ(.*?)(;.*)$/$1a$2③$3/"
    - "xform/^(.*)à(.*?)(;.*)$/$1a$2④$3/"
    - "xform/^(.*)ō(.*?)(;.*)$/$1o$2①$3/"
    - "xform/^(.*)ó(.*?)(;.*)$/$1o$2②$3/"
    - "xform/^(.*)ǒ(.*?)(;.*)$/$1o$2③$3/"
    - "xform/^(.*)ò(.*?)(;.*)$/$1o$2④$3/"
    - "xform/^(.*)ē(.*?)(;.*)$/$1e$2①$3/"
    - "xform/^(.*)é(.*?)(;.*)$/$1e$2②$3/"
    - "xform/^(.*)ě(.*?)(;.*)$/$1e$2③$3/"
    - "xform/^(.*)è(.*?)(;.*)$/$1e$2④$3/"
    - "xform/^(.*)ī(.*?)(;.*)$/$1i$2①$3/"
    - "xform/^(.*)í(.*?)(;.*)$/$1i$2②$3/"
    - "xform/^(.*)ǐ(.*?)(;.*)$/$1i$2③$3/"
    - "xform/^(.*)ì(.*?)(;.*)$/$1i$2④$3/"
    - "xform/^(.*)ū(.*?)(;.*)$/$1u$2①$3/"
    - "xform/^(.*)ú(.*?)(;.*)$/$1u$2②$3/"
    - "xform/^(.*)ǔ(.*?)(;.*)$/$1u$2③$3/"
    - "xform/^(.*)ù(.*?)(;.*)$/$1u$2④$3/"
    - "xform/^(.*)ǖ(.*?)(;.*)$/$1v$2①$3/"
    - "xform/^(.*)ǘ(.*?)(;.*)$/$1v$2②$3/"
    - "xform/^(.*)ǚ(.*?)(;.*)$/$1v$2③$3/"
    - "xform/^(.*)ǜ(.*?)(;.*)$/$1v$2④$3/"
    - "xform/^(.*)ü(.*?)$/$1v$2/"
    - "xform/^(.*)ń(.*?)(;.*)$/$1n$2②$3/"
    - "xform/^(.*)ň(.*?)(;.*)$/$1n$2③$3/"
    - "xform/^(.*)ǹ(.*?)(;.*)$/$1n$2④$3/"
    - "xlit/①②③④/7890"
    - "derive/^ng(\\d)(;.*)$/eng$1$2/"
    - "xform/^n(\\d)(;.*)/en$1$2/"
    - "derive/^([jqxy])u(\\d)(;.*)$/$1v$2$3/"
    - "derive/^([aoe])([ioun])(\\d)(;.*)$/$1$1$2$3$4/"
    - "xform/^([aoe])(ng)?(\\d)(;.*)$/$1$1$2$3$4/"
    - "xform/^(\\w+?)[iu]a(\\d)(;.*)$/$1Ⓠ$2$3/"
    - "xform/^(\\w+?)[vu]an(\\d)(;.*)$/$1Ⓦ$2$3/"
    - "xform/^(\\w+?)en(\\d)(;.*)$/$1Ⓡ$2$3/"
    - "xform/^(\\w+?)ie(\\d)(;.*)$/$1Ⓣ$2$3/"
    - "xform/^(\\w+?)(iu|uai)(\\d)(;.*)$/$1Ⓨ$3$4/"
    - "xform/^(\\w+?)uo(\\d)(;.*)$/$1Ⓞ$2$3/"
    - "xform/^(\\w+?)ou(\\d)(;.*)$/$1Ⓟ$2$3/"
    - "xform/^(\\w+?)i?ong(\\d)(;.*)$/$1Ⓢ$2$3/"
    - "xform/^(\\w+?)ian(\\d)(;.*)$/$1Ⓓ$2$3/"
    - "xform/^(\\w+?)an(\\d)(;.*)$/$1Ⓕ$2$3/"
    - "xform/^(\\w+?)(iang|uang)(\\d)(;.*)$/$1Ⓝ$3$4/"
    - "xform/^(\\w+?)iao(\\d)(;.*)$/$1Ⓜ$2$3/"
    - "xform/^(\\w+?)ang(\\d)(;.*)$/$1Ⓖ$2$3/"
    - "xform/^(\\w+?)eng(\\d)(;.*)$/$1Ⓗ$2$3/"
    - "xform/^(\\w+?)ing(\\d)(;.*)$/$1Ⓙ$2$3/"
    - "xform/^(\\w+?)ai(\\d)(;.*)$/$1Ⓚ$2$3/"
    - "xform/^(\\w+?)(in|er)(\\d)(;.*)$/$1Ⓛ$3$4/"
    - "xform/^(\\w+?)[vu]n(\\d)(;.*)$/$1Ⓩ$2$3/"
    - "xform/^(\\w+?)[vu]e(\\d)(;.*)$/$1Ⓧ$2$3/"
    - "xform/^(\\w+?)ao(\\d)(;.*)$/$1Ⓒ$2$3/"
    - "xform/^(\\w+?)(v|ui)(\\d)(;.*)$/$1Ⓥ$3$4/"
    - "xform/^(\\w+?)ei(\\d)(;.*)$/$1Ⓑ$2$3/"
    - "xform/^zh/Ⓥ/"
    - "xform/^ch/Ⓘ/"
    - "xform/^sh/Ⓤ/"
    - "xlit/ⓆⓌⓇⓉⓎⓊⒾⓄⓅⓈⒹⒻⒼⒽⒿⓀⓁⓏⓍⒸⓋⒷⓃⓂ/qwrtyuiopsdfghjklzxcvbnm/"
"小鹤双拼":
  __append:
    - "xform/^([a-z]+)(;.*?)$/$1④$2/"
    - "xform/^(.*)ā(.*?)(;.*)$/$1a$2①$3/"
    - "xform/^(.*)á(.*?)(;.*)$/$1a$2②$3/"
    - "xform/^(.*)ǎ(.*?)(;.*)$/$1a$2③$3/"
    - "xform/^(.*)à(.*?)(;.*)$/$1a$2④$3/"
    - "xform/^(.*)ō(.*?)(;.*)$/$1o$2①$3/"
    - "xform/^(.*)ó(.*?)(;.*)$/$1o$2②$3/"
    - "xform/^(.*)ǒ(.*?)(;.*)$/$1o$2③$3/"
    - "xform/^(.*)ò(.*?)(;.*)$/$1o$2④$3/"
    - "xform/^(.*)ē(.*?)(;.*)$/$1e$2①$3/"
    - "xform/^(.*)é(.*?)(;.*)$/$1e$2②$3/"
    - "xform/^(.*)ě(.*?)(;.*)$/$1e$2③$3/"
    - "xform/^(.*)è(.*?)(;.*)$/$1e$2④$3/"
    - "xform/^(.*)ī(.*?)(;.*)$/$1i$2①$3/"
    - "xform/^(.*)í(.*?)(;.*)$/$1i$2②$3/"
    - "xform/^(.*)ǐ(.*?)(;.*)$/$1i$2③$3/"
    - "xform/^(.*)ì(.*?)(;.*)$/$1i$2④$3/"
    - "xform/^(.*)ū(.*?)(;.*)$/$1u$2①$3/"
    - "xform/^(.*)ú(.*?)(;.*)$/$1u$2②$3/"
    - "xform/^(.*)ǔ(.*?)(;.*)$/$1u$2③$3/"
    - "xform/^(.*)ù(.*?)(;.*)$/$1u$2④$3/"
    - "xform/^(.*)ǖ(.*?)(;.*)$/$1v$2①$3/"
    - "xform/^(.*)ǘ(.*?)(;.*)$/$1v$2②$3/"
    - "xform/^(.*)ǚ(.*?)(;.*)$/$1v$2③$3/"
    - "xform/^(.*)ǜ(.*?)(;.*)$/$1v$2④$3/"
    - "xform/^(.*)ü(.*?)$/$1v$2/"
    - "xform/^(.*)ń(.*?)(;.*)$/$1n$2②$3/"
    - "xform/^(.*)ň(.*?)(;.*)$/$1n$2③$3/"
    - "xform/^(.*)ǹ(.*?)(;.*)$/$1n$2④$3/"
    - "xlit/①②③④/7890"
    - "derive/^ng(\\d)(;.*)$/eng$1$2/"
    - "xform/^n(\\d)(;.*)/en$1$2/"
    - "derive/^([jqxy])u(\\d)(;.*)$/$1v$2$3/"
    - "derive/^([aoe])([ioun])(\\d)(;.*)$/$1$1$2$3$4/"
    - "xform/^([aoe])(ng)?(\\d)(;.*)$/$1$1$2$3$4/"
    - "xform/^(\\w+?)iu(\\d)(;.*)/$1Ⓠ$2$3/"
    - "xform/^(\\w+?)ei(\\d)(;.*)/$1Ⓦ$2$3/"
    - "xform/^(\\w+?)uan(\\d)(;.*)/$1Ⓡ$2$3/"
    - "xform/^(\\w+?)[uv]e(\\d)(;.*)/$1Ⓣ$2$3/"
    - "xform/^(\\w+?)un(\\d)(;.*)/$1Ⓨ$2$3/"
    - "xform/^(\\w+?)uo(\\d)(;.*)/$1Ⓞ$2$3/"
    - "xform/^(\\w+?)ie(\\d)(;.*)/$1Ⓟ$2$3/"
    - "xform/^(\\w+?)i?ong(\\d)(;.*)/$1Ⓢ$2$3/"
    - "xform/^(\\w+?)ing(\\d)(;.*)/$1Ⓚ$2$3/"
    - "xform/^(\\w+?)uai(\\d)(;.*)/$1Ⓚ$2$3/"
    - "xform/^(\\w+?)ai(\\d)(;.*)/$1Ⓓ$2$3/"
    - "xform/^(\\w+?)eng(\\d)(;.*)/$1Ⓖ$2$3/"
    - "xform/^(\\w+?)en(\\d)(;.*)/$1Ⓕ$2$3/"
    - "xform/^(\\w+?)[iu]ang(\\d)(;.*)/$1Ⓛ$2$3/"
    - "xform/^(\\w+?)ang(\\d)(;.*)/$1Ⓗ$2$3/"
    - "xform/^(\\w+?)ian(\\d)(;.*)/$1Ⓜ$2$3/"
    - "xform/^(\\w+?)an(\\d)(;.*)/$1Ⓙ$2$3/"
    - "xform/^(\\w+?)ou(\\d)(;.*)/$1Ⓩ$2$3/"
    - "xform/^(\\w+?)iao(\\d)(;.*)/$1Ⓝ$2$3/"
    - "xform/^(\\w+?)[iu]a(\\d)(;.*)/$1Ⓧ$2$3/"
    - "xform/^(\\w+?)ao(\\d)(;.*)/$1Ⓒ$2$3/"
    - "xform/^(\\w+?)ui(\\d)(;.*)/$1Ⓥ$2$3/"
    - "xform/^(\\w+?)in(\\d)(;.*)/$1Ⓑ$2$3/"
    - "xform/^sh/Ⓤ/"
    - "xform/^ch/Ⓘ/"
    - "xform/^zh/Ⓥ/"
    - "xlit/ⓆⓌⓇⓉⓎⓊⒾⓄⓅⓈⒹⒻⒼⒽⒿⓀⓁⓏⓍⒸⓋⒷⓃⓂ/qwrtyuiopsdfghjklzxcvbnm/"
"微软双拼":
  __append:
    - "xform/^([a-z]+)(;.*?)$/$1④$2/"
    - "xform/^(.*)ā(.*?)(;.*)$/$1a$2①$3/"
    - "xform/^(.*)á(.*?)(;.*)$/$1a$2②$3/"
    - "xform/^(.*)ǎ(.*?)(;.*)$/$1a$2③$3/"
    - "xform/^(.*)à(.*?)(;.*)$/$1a$2④$3/"
    - "xform/^(.*)ō(.*?)(;.*)$/$1o$2①$3/"
    - "xform/^(.*)ó(.*?)(;.*)$/$1o$2②$3/"
    - "xform/^(.*)ǒ(.*?)(;.*)$/$1o$2③$3/"
    - "xform/^(.*)ò(.*?)(;.*)$/$1o$2④$3/"
    - "xform/^(.*)ē(.*?)(;.*)$/$1e$2①$3/"
    - "xform/^(.*)é(.*?)(;.*)$/$1e$2②$3/"
    - "xform/^(.*)ě(.*?)(;.*)$/$1e$2③$3/"
    - "xform/^(.*)è(.*?)(;.*)$/$1e$2④$3/"
    - "xform/^(.*)ī(.*?)(;.*)$/$1i$2①$3/"
    - "xform/^(.*)í(.*?)(;.*)$/$1i$2②$3/"
    - "xform/^(.*)ǐ(.*?)(;.*)$/$1i$2③$3/"
    - "xform/^(.*)ì(.*?)(;.*)$/$1i$2④$3/"
    - "xform/^(.*)ū(.*?)(;.*)$/$1u$2①$3/"
    - "xform/^(.*)ú(.*?)(;.*)$/$1u$2②$3/"
    - "xform/^(.*)ǔ(.*?)(;.*)$/$1u$2③$3/"
    - "xform/^(.*)ù(.*?)(;.*)$/$1u$2④$3/"
    - "xform/^(.*)ǖ(.*?)(;.*)$/$1v$2①$3/"
    - "xform/^(.*)ǘ(.*?)(;.*)$/$1v$2②$3/"
    - "xform/^(.*)ǚ(.*?)(;.*)$/$1v$2③$3/"
    - "xform/^(.*)ǜ(.*?)(;.*)$/$1v$2④$3/"
    - "xform/^(.*)ü(.*?)$/$1v$2/"
    - "xform/^(.*)ń(.*?)(;.*)$/$1n$2②$3/"
    - "xform/^(.*)ň(.*?)(;.*)$/$1n$2③$3/"
    - "xform/^(.*)ǹ(.*?)(;.*)$/$1n$2④$3/"
    - "xlit/①②③④/7890"
    - "derive/^ng(\\d)(;.*)$/eng$1$2/"
    - "xform/^n(\\d)(;.*)/en$1$2/"
    - "derive/^([jqxy])u(\\d)(;.*)$/$1v$2$3/"
    - "derive/^([aoe].*)(\\d)(;.*)$/o$1$2$3/"
    - "xform/^([ae])(.*)(\\d)(;.*)$/$1$1$2$3$3/"
    - "xform/^(\\w+?)iu(\\d)(;.*)/$1Ⓠ$2$3/"
    - "xform/^(\\w+?)er(\\d)(;.*)/$1Ⓡ$2$3/"
    - "xform/^(\\w+?)[uv]an(\\d)(;.*)/$1Ⓡ$2$3/"
    - "xform/^(\\w+?)[uv]e(\\d)(;.*)/$1Ⓣ$2$3/"
    - "xform/^(\\w+?)v(\\d)(;.*)/$1Ⓨ$2$3/"
    - "xform/^(\\w+?)uai(\\d)(;.*)/$1Ⓨ$2$3/"
    - "xform/^(\\w+?)uo(\\d)(;.*)/$1Ⓞ$2$3/"
    - "xform/^(\\w+?)[uv]n(\\d)(;.*)/$1Ⓟ$2$3/"
    - "xform/^(\\w+?)i?ong(\\d)(;.*)/$1Ⓢ$2$3/"
    - "xform/^(\\w+?)[iu]ang(\\d)(;.*)/$1Ⓓ$2$3/"
    - "xform/^(\\w+?)eng(\\d)(;.*)/$1Ⓖ$2$3/"
    - "xform/^(\\w+?)en(\\d)(;.*)/$1Ⓕ$2$3/"
    - "xform/^(\\w+?)ang(\\d)(;.*)/$1Ⓗ$2$3/"
    - "xform/^(\\w+?)ian(\\d)(;.*)/$1Ⓜ$2$3/"
    - "xform/^(\\w+?)an(\\d)(;.*)/$1Ⓙ$2$3/"
    - "xform/^(\\w+?)iao(\\d)(;.*)/$1Ⓒ$2$3/"
    - "xform/^(\\w+?)ao(\\d)(;.*)/$1Ⓚ$2$3/"
    - "xform/^(\\w+?)ai(\\d)(;.*)/$1Ⓛ$2$3/"
    - "xform/^(\\w+?)ei(\\d)(;.*)/$1Ⓩ$2$3/"
    - "xform/^(\\w+?)ie(\\d)(;.*)/$1Ⓧ$2$3/"
    - "xform/^(\\w+?)ui(\\d)(;.*)/$1Ⓥ$2$3/"
    - "derive/Ⓣ/Ⓥ/"
    - "xform/^(\\w+?)ou(\\d)(;.*)/$1Ⓑ$2$3/"
    - "xform/^(\\w+?)ing(\\d)(;.*)/$1◯$2$3/"
    - "xform/^(\\w+?)in(\\d)(;.*)/$1Ⓝ$2$3/"
    - "xform/^(\\w+?)[iu]a(\\d)(;.*)/$1Ⓦ$2$3/"
    - "xform/^sh/Ⓤ/"
    - "xform/^ch/Ⓘ/"
    - "xform/^zh/Ⓥ/"
    - "xlit/ⓆⓌⓇⓉⓎⓊⒾⓄⓅⓈⒹⒻⒼⒽⓂⒿⒸⓀⓁⓏⓍⓋⒷⓃ/qwrtyuiopsdfghmjcklzxvbn/"
"搜狗双拼":
  __append:
    - "xform/^([a-z]+)(;.*?)$/$1④$2/"
    - "xform/^(.*)ā(.*?)(;.*)$/$1a$2①$3/"
    - "xform/^(.*)á(.*?)(;.*)$/$1a$2②$3/"
    - "xform/^(.*)ǎ(.*?)(;.*)$/$1a$2③$3/"
    - "xform/^(.*)à(.*?)(;.*)$/$1a$2④$3/"
    - "xform/^(.*)ō(.*?)(;.*)$/$1o$2①$3/"
    - "xform/^(.*)ó(.*?)(;.*)$/$1o$2②$3/"
    - "xform/^(.*)ǒ(.*?)(;.*)$/$1o$2③$3/"
    - "xform/^(.*)ò(.*?)(;.*)$/$1o$2④$3/"
    - "xform/^(.*)ē(.*?)(;.*)$/$1e$2①$3/"
    - "xform/^(.*)é(.*?)(;.*)$/$1e$2②$3/"
    - "xform/^(.*)ě(.*?)(;.*)$/$1e$2③$3/"
    - "xform/^(.*)è(.*?)(;.*)$/$1e$2④$3/"
    - "xform/^(.*)ī(.*?)(;.*)$/$1i$2①$3/"
    - "xform/^(.*)í(.*?)(;.*)$/$1i$2②$3/"
    - "xform/^(.*)ǐ(.*?)(;.*)$/$1i$2③$3/"
    - "xform/^(.*)ì(.*?)(;.*)$/$1i$2④$3/"
    - "xform/^(.*)ū(.*?)(;.*)$/$1u$2①$3/"
    - "xform/^(.*)ú(.*?)(;.*)$/$1u$2②$3/"
    - "xform/^(.*)ǔ(.*?)(;.*)$/$1u$2③$3/"
    - "xform/^(.*)ù(.*?)(;.*)$/$1u$2④$3/"
    - "xform/^(.*)ǖ(.*?)(;.*)$/$1v$2①$3/"
    - "xform/^(.*)ǘ(.*?)(;.*)$/$1v$2②$3/"
    - "xform/^(.*)ǚ(.*?)(;.*)$/$1v$2③$3/"
    - "xform/^(.*)ǜ(.*?)(;.*)$/$1v$2④$3/"
    - "xform/^(.*)ü(.*?)$/$1v$2/"
    - "xform/^(.*)ń(.*?)(;.*)$/$1n$2②$3/"
    - "xform/^(.*)ň(.*?)(;.*)$/$1n$2③$3/"
    - "xform/^(.*)ǹ(.*?)(;.*)$/$1n$2④$3/"
    - "xlit/①②③④/7890"
    - "derive/^ng(\\d)(;.*)$/eng$1$2/"
    - "xform/^n(\\d)(;.*)/en$1$2/"
    - "derive/^([jqxy])u(\\d)(;.*)$/$1v$2$3/"
    - "derive/^([aoe].*)(\\d)(;.*)$/o$1$2$3/"
    - "xform/^([ae])(.*)(\\d)(;.*)$/$1$1$2$3$4/"
    - "xform/^(\\w+?)iu(\\d)(;.*)$/$1Ⓠ$2$3/"
    - "xform/^(\\w+?)[iu]a(\\d)(;.*)$/$1Ⓦ$2$3/"
    - "xform/^(\\w+?)er(\\d)(;.*)$/$1Ⓡ$2$3/"
    - "xform/^(\\w+?)[uv]an(\\d)(;.*)$/$1Ⓡ$2$3/"
    - "xform/^(\\w+?)[uv]e(\\d)(;.*)$/$1Ⓣ$2$3/"
    - "xform/^(\\w+?)v(\\d)(;.*)$/$1Ⓨ$2$3/"
    - "xform/^(\\w+?)uai(\\d)(;.*)$/$1Ⓨ$2$3/"
    - "xform/^(\\w+?)uo(\\d)(;.*)$/$1Ⓞ$2$3/"
    - "xform/^(\\w+?)[uv]n(\\d)(;.*)$/$1Ⓟ$2$3/"
    - "xform/^(\\w+?)i?ong(\\d)(;.*)$/$1Ⓢ$2$3/"
    - "xform/^(\\w+?)[iu]ang(\\d)(;.*)$/$1Ⓓ$2$3/"
    - "xform/^(\\w+?)en(\\d)(;.*)$/$1Ⓕ$2$3/"
    - "xform/^(\\w+?)eng(\\d)(;.*)$/$1Ⓖ$2$3/"
    - "xform/^(\\w+?)ang(\\d)(;.*)$/$1Ⓗ$2$3/"
    - "xform/^(\\w+?)ian(\\d)(;.*)$/$1Ⓜ$2$3/"
    - "xform/^(\\w+?)an(\\d)(;.*)$/$1Ⓙ$2$3/"
    - "xform/^(\\w+?)iao(\\d)(;.*)$/$1Ⓒ$2$3/"
    - "xform/^(\\w+?)ao(\\d)(;.*)$/$1Ⓚ$2$3/"
    - "xform/^(\\w+?)ai(\\d)(;.*)$/$1Ⓛ$2$3/"
    - "xform/^(\\w+?)ei(\\d)(;.*)$/$1Ⓩ$2$3/"
    - "xform/^(\\w+?)ie(\\d)(;.*)$/$1Ⓧ$2$3/"
    - "xform/^(\\w+?)ui(\\d)(;.*)$/$1Ⓥ$2$3/"
    - "xform/^(\\w+?)ou(\\d)(;.*)$/$1Ⓑ$2$3/"
    - "xform/^(\\w+?)in(\\d)(;.*)$/$1Ⓝ$2$3/"
    - "xform/^(\\w+?)ing(\\d)(;.*)$/$1◯$2$3/"
    - "xform/^sh/Ⓤ/"
    - "xform/^ch/Ⓘ/"
    - "xform/^zh/Ⓥ/"
    - "xlit/ⓆⓌⓇⓉⓎⓊⒾⓄⓅⓈⒹⒻⒼⒽⓂⒿⒸⓀⓁⓏⓍⓋⒷⓃ/qwrtyuiopsdfghmjcklzxvbn/"
"智能ABC":
  __append:
    - "xform/^([a-z]+)(;.*?)$/$1④$2/"
    - "xform/^(.*)ā(.*?)(;.*)$/$1a$2①$3/"
    - "xform/^(.*)á(.*?)(;.*)$/$1a$2②$3/"
    - "xform/^(.*)ǎ(.*?)(;.*)$/$1a$2③$3/"
    - "xform/^(.*)à(.*?)(;.*)$/$1a$2④$3/"
    - "xform/^(.*)ō(.*?)(;.*)$/$1o$2①$3/"
    - "xform/^(.*)ó(.*?)(;.*)$/$1o$2②$3/"
    - "xform/^(.*)ǒ(.*?)(;.*)$/$1o$2③$3/"
    - "xform/^(.*)ò(.*?)(;.*)$/$1o$2④$3/"
    - "xform/^(.*)ē(.*?)(;.*)$/$1e$2①$3/"
    - "xform/^(.*)é(.*?)(;.*)$/$1e$2②$3/"
    - "xform/^(.*)ě(.*?)(;.*)$/$1e$2③$3/"
    - "xform/^(.*)è(.*?)(;.*)$/$1e$2④$3/"
    - "xform/^(.*)ī(.*?)(;.*)$/$1i$2①$3/"
    - "xform/^(.*)í(.*?)(;.*)$/$1i$2②$3/"
    - "xform/^(.*)ǐ(.*?)(;.*)$/$1i$2③$3/"
    - "xform/^(.*)ì(.*?)(;.*)$/$1i$2④$3/"
    - "xform/^(.*)ū(.*?)(;.*)$/$1u$2①$3/"
    - "xform/^(.*)ú(.*?)(;.*)$/$1u$2②$3/"
    - "xform/^(.*)ǔ(.*?)(;.*)$/$1u$2③$3/"
    - "xform/^(.*)ù(.*?)(;.*)$/$1u$2④$3/"
    - "xform/^(.*)ǖ(.*?)(;.*)$/$1v$2①$3/"
    - "xform/^(.*)ǘ(.*?)(;.*)$/$1v$2②$3/"
    - "xform/^(.*)ǚ(.*?)(;.*)$/$1v$2③$3/"
    - "xform/^(.*)ǜ(.*?)(;.*)$/$1v$2④$3/"
    - "xform/^(.*)ü(.*?)$/$1v$2/"
    - "xform/^(.*)ń(.*?)(;.*)$/$1n$2②$3/"
    - "xform/^(.*)ň(.*?)(;.*)$/$1n$2③$3/"
    - "xform/^(.*)ǹ(.*?)(;.*)$/$1n$2④$3/"
    - "xlit/①②③④/7890"
    - "derive/^ng(\\d)(;.*)$/eng$1$2/"
    - "xform/^n(\\d)(;.*)/en$1$2/"
    - "xform/^([aoe].*)(\\d)(;.*)$/Ⓞ$1$2$3/"
    - "xform/^(\\w+?)ei(\\d)(;.*)$/$1Ⓠ$2$3/"
    - "xform/^(\\w+?)ian(\\d)(;.*)$/$1Ⓦ$2$3/"
    - "xform/^(\\w+?)er(\\d)(;.*)$/$1Ⓡ$2$3/"
    - "xform/^(\\w+?)iu(\\d)(;.*)$/$1Ⓡ$2$3/"
    - "xform/^(\\w+?)[iu]ang(\\d)(;.*)$/$1Ⓣ$2$3/"
    - "xform/^(\\w+?)ing(\\d)(;.*)$/$1Ⓨ$2$3/"
    - "xform/^(\\w+?)uo(\\d)(;.*)$/$1Ⓞ$2$3/"
    - "xform/^(\\w+?)uan(\\d)(;.*)$/$1Ⓟ$2$3/"
    - "xform/^(\\w+?)i?ong(\\d)(;.*)$/$1Ⓢ$2$3/"
    - "xform/^(\\w+?)[iu]a(\\d)(;.*)$/$1Ⓓ$2$3/"
    - "xform/^(\\w+?)en(\\d)(;.*)$/$1Ⓕ$2$3/"
    - "xform/^(\\w+?)eng(\\d)(;.*)$/$1Ⓖ$2$3/"
    - "xform/^(\\w+?)ang(\\d)(;.*)$/$1Ⓗ$2$3/"
    - "xform/^(\\w+?)an(\\d)(;.*)$/$1Ⓙ$2$3/"
    - "xform/^(\\w+?)iao(\\d)(;.*)$/$1Ⓩ$2$3/"
    - "xform/^(\\w+?)ao(\\d)(;.*)$/$1Ⓚ$2$3/"
    - "xform/^(\\w+?)in(\\d)(;.*)$/$1Ⓒ$2$3/"
    - "xform/^(\\w+?)uai(\\d)(;.*)$/$1Ⓒ$2$3/"
    - "xform/^(\\w+?)ai(\\d)(;.*)$/$1Ⓛ$2$3/"
    - "xform/^(\\w+?)ie(\\d)(;.*)$/$1Ⓧ$2$3/"
    - "xform/^(\\w+?)ou(\\d)(;.*)$/$1Ⓑ$2$3/"
    - "xform/^(\\w+?)un(\\d)(;.*)$/$1Ⓝ$2$3/"
    - "xform/^(\\w+?)[uv]e(\\d)(;.*)$/$1Ⓜ$2$3/"
    - "xform/^(\\w+?)ui(\\d)(;.*)$/$1Ⓜ$2$3/"
    - "xform/^zh/Ⓐ/"
    - "xform/^ch/Ⓔ/"
    - "xform/^sh/Ⓥ/"
    - "xlit/ⓆⓌⒺⓇⓉⓎⓄⓅⒶⓈⒹⒻⒼⒽⒿⓀⓁⓏⓍⒸⓋⒷⓃⓂ/qwertyopasdfghjklzxcvbnm/"
"汉心龙":
  __append:
    - "xform/^ēr/BA/"
    - "xform/^ér/BJ/"
    - "xform/^ěr/BP/"
    - "xform/^èr/BO/"
    - "xform/^er/BA/"
    - "xform/^(ā|á|ǎ|à)([ioun])/Q$1$2/"
    - "xform/^(ō|ó|ǒ|ò)([ioun])/R$1$2/"
    - "xform/^(ē|é|ě|è)([ioun])/B$1$2/"
    - "xform/^(ā|á|ǎ|à)(ng)/Q$1$2/"
    - "xform/^(ō|ó|ǒ|ò)(ng)/R$1$2/"
    - "xform/^(ē|é|ě|è)(ng)/B$1$2/"
    - "xform/^(ā|á|ǎ|à)/Q$1/"
    - "xform/^(ō|ó|ǒ|ò)/R$1/"
    - "xform/^(ē|é|ě|è)/B$1/"
    - "xform/^([jqxy])u(;.*)/$1ü$2/"
    - "xform/^([jqxy])ū(;.*)/$1ǖ$2/"
    - "xform/^([jqxy])ú(;.*)/$1ǘ$2/"
    - "xform/^([jqxy])ǔ(;.*)/$1ǚ$2/"
    - "xform/^([jqxy])ù(;.*)/$1ǜ$2/"
    - "xform/^a(;.*)$/Qā$1/"
    - "xform/^o(;.*)$/Rō$1/"
    - "xform/^e(;.*)$/Bē$1/"
    - "xform/^ǹg/Bèng/"
    - "xform/^ňg/Běng/"
    - "xform/^ńg/Béng/"
    - "xform/^ng/beng/"
    - "xform/^ǹ/Bèn/"
    - "xform/^ň/Běn/"
    - "xform/^ń/Bén/"
    - "xform/^n(;.*)/Ben$1/"
    - "xform/^sh/T/"
    - "xform/^ch/S/"
    - "xform/^zh/K/"
    - "xform/^a/Q/"
    - "xform/^p/W/"
    - "xform/^j/E/"
    - "xform/^o/R/"
    - "xform/^n/Y/"
    - "xform/^k/U/"
    - "xform/^b/I/"
    - "xform/^t/O/"
    - "xform/^m/P/"
    - "xform/^q/A/"
    - "xform/^l/D/"
    - "xform/^r/F/"
    - "xform/^w/G/"
    - "xform/^d/H/"
    - "xform/^y/J/"
    - "xform/^g/L/"
    - "xform/^s/Z/"
    - "xform/^c/X/"
    - "xform/^x/C/"
    - "xform/^f/V/"
    - "xform/^e/B/"
    - "xform/^z/N/"
    - "xform/^h/M/"
    - "xform/^(.)(ēn|en|iǎn|ǒng|iáng|uá|ó|án|ǐng)(;.*)$/$1Q$3/"
    - "xform/^(.)(ào|èn|èng|iě|iàng)(;.*)$/$1W$3/"
    - "xform/^(.)(ǖ|iù|ā|a|uì|uǎng)(;.*)$/$1E$3/"
    - "xform/^(.)(ìn|è|à|ě|ǎi|áo)(;.*)$/$1R$3/"
    - "xform/^(.)(ō|o|ìòng|ǎo|óu)(;.*)$/$1T$3/"
    - "xform/^(.)(én|ià|ang|āng|iú|ǐn|üē|uē|üe)(;.*)$/$1Y$3/"
    - "xform/^(.)(àng|iàn|ao|āo)(;.*)$/$1U$3/"
    - "xform/^(.)(ù|uán|ué|üé)(;.*)$/$1I$3/"
    - "xform/^(.)(éng|iā|ia|ān|an|èr|uè|üè|iū|uài|iáo)(;.*)$/$1O$3/"
    - "xform/^(.)(uǒ|iè|iào|ěr|ǎng)(;.*)$/$1P$3/"
    - "xform/^(.)(āi|ai|óng|ìng|uāng|uang|á|ēr|er|iǎ)(;.*)$/$1A$3/"
    - "xform/^(.)(uō|uo|ǚ|uó|àn|ín|ūn|un|iōng|iong)(;.*)$/$1S$3/"
    - "xform/^(.)(ǒu|èi|é|iāo|iao|iá)(;.*)$/$1D$3/"
    - "xform/^(.)(ǐ|uān|uan|uà|ò|uǎ)(;.*)$/$1F$3/"
    - "xform/^(.)(ī|i|ěn|ěi|uáng)(;.*)$/$1G$3/"
    - "xform/^(.)(ǜ|ēng|eng|iē|ie|éi|ún|uāi|uai)(;.*)$/$1H$3/"
    - "xform/^(.)(ái|ǒ|ū|u|ér|īng|ing|uǎi)(;.*)$/$1J$3/"
    - "xform/^(.)(ē|e|íng|òu|ié)(;.*)$/$1K$3/"
    - "xform/^(.)(ì|uī|ui)(;.*)$/$1L$3/"
    - "xform/^(.)(uā|ua|òng|uǐ|áng|iǔ|iǎo)(;.*)$/$1Z$3/"
    - "xform/^(.)(uò|ōu|iān|ian|ēi|ei|ùn)(;.*)$/$1X$3/"
    - "xform/^(.)(ǔ|uàn)(;.*)$/$1C$3/"
    - "xform/^(.)(ǎ|uái|ōng|ong|īn|in|ióng|iǒng)(;.*)$/$1V$3/"
    - "xform/^(.)(ài|ián|üě|ǔe|uí|uǎn|uàng|ǘ)(;.*)$/$1B$3/"
    - "xform/^(.)(í|iǎng|ǎn)(;.*)$/$1N$3/"
    - "xform/^(.)(ú|iāng|iang|ěng|ǔn)(;.*)$/$1M$3/"
    - "xlit/QWERTYUIOPASDFGHMJCKLZXVBN/qwertyuiopasdfghmjcklzxvbn/"
"直接辅助":
  __append:
    - "derive/^(.*?)(\\d?);.*$/$1/"
    - "derive/^(.*?)(\\d?);.*$/$1$2/"
    - "derive/^(.*?)(\\d?);(\\w).*$/$1$3/"
    - "derive|^(.*?)(\\d?);(\\w).*$|$1$2$3|"
    - "derive|^(.*?)(\\d?);(\\w).*$|$1$3$2|"
    - "abbrev/^(.*?)(\\d?);(\\w)(\\w).*$/$1$3$4/"
    - "derive|^(.*?)(\\d?);(\\w)(\\w).*$|$1$3$4$2|"
    - "derive|^(.*?)(\\d?);(\\w)(\\w).*$|$1$2$3$4|"
    - "derive|^(.*?)(\\d?);(\\w)(\\w).*$|$1$3$2$4|"
    - "derive|^(.*?)(\\d?);(\\w)(\\w).*$|$1$3$4/|"
    - "derive|^(.*?)(\\d?);(\\w)(\\w).*$|$1$3$4o|"
    - "derive/^(.*?)(\\d?);.*?,(\\w).*$/$1$3/"
    - "derive|^(.*?)(\\d?);.*?,(\\w).*$|$1$2$3|"
    - "derive|^(.*?)(\\d?);.*?,(\\w).*$|$1$3$2|"
    - "abbrev/^(.*?)(\\d?);.*?,(\\w)(\\w).*$/$1$3$4/"
    - "derive|^(.*?)(\\d?);.*?,(\\w)(\\w).*$|$1$3$4$2|"
    - "derive|^(.*?)(\\d?);.*?,(\\w)(\\w).*$|$1$2$3$4|"
    - "derive|^(.*?)(\\d?);.*?,(\\w)(\\w).*$|$1$3$2$4|"
    - "derive|^(.*?)(\\d?);.*?,(\\w)(\\w).*$|$1$3$4/|"
    - "derive|^(.*?)(\\d?);.*?,(\\w)(\\w).*$|$1$3$4o|"
    - "derive/^(.*?)(\\d?);.*?,.*?,(\\w).*$/$1$3/"
    - "derive|^(.*?)(\\d?);.*?,.*?,(\\w).*$|$1$2$3|"
    - "derive|^(.*?)(\\d?);.*?,.*?,(\\w).*$|$1$3$2|"
    - "abbrev/^(.*?)(\\d?);.*?,.*?,(\\w)(\\w).*$/$1$3$4/"
    - "derive|^(.*?)(\\d?);.*?,.*?,(\\w)(\\w).*$|$1$3$4$2|"
    - "derive|^(.*?)(\\d?);.*?,.*?,(\\w)(\\w).*$|$1$2$3$4|"
    - "derive|^(.*?)(\\d?);.*?,.*?,(\\w)(\\w).*$|$1$3$2$4|"
    - "derive|^(.*?)(\\d?);.*?,.*?,(\\w)(\\w).*$|$1$3$4/|"
    - "derive|^(.*?)(\\d?);.*?,.*?,(\\w)(\\w).*$|$1$3$4o|"
    - "derive/^(.*?)(\\d?);.*?,.*?,.*?,(\\w).*$/$1$3/"
    - "derive|^(.*?)(\\d?);.*?,.*?,.*?,(\\w).*$|$1$2$3|"
    - "derive|^(.*?)(\\d?);.*?,.*?,.*?,(\\w).*$|$1$3$2|"
    - "abbrev/^(.*?)(\\d?);.*?,.*?,.*?,(\\w)(\\w).*$/$1$3$4/"
    - "derive|^(.*?)(\\d?);.*?,.*?,.*?,(\\w)(\\w).*$|$1$3$4$2|"
    - "derive|^(.*?)(\\d?);.*?,.*?,.*?,(\\w)(\\w).*$|$1$2$3$4|"
    - "derive|^(.*?)(\\d?);.*?,.*?,.*?,(\\w)(\\w).*$|$1$3$2$4|"
    - "derive|^(.*?)(\\d?);.*?,.*?,.*?,(\\w)(\\w).*$|$1$3$4/|"
    - "derive|^(.*?)(\\d?);.*?,.*?,.*?,(\\w)(\\w).*$|$1$3$4o|"
    - "xform/◯/;/"
    - "erase/^(.+);(.+)$/"
"紫光双拼":
  __append:
    - "xform/^([a-z]+)(;.*?)$/$1④$2/"
    - "xform/^(.*)ā(.*?)(;.*)$/$1a$2①$3/"
    - "xform/^(.*)á(.*?)(;.*)$/$1a$2②$3/"
    - "xform/^(.*)ǎ(.*?)(;.*)$/$1a$2③$3/"
    - "xform/^(.*)à(.*?)(;.*)$/$1a$2④$3/"
    - "xform/^(.*)ō(.*?)(;.*)$/$1o$2①$3/"
    - "xform/^(.*)ó(.*?)(;.*)$/$1o$2②$3/"
    - "xform/^(.*)ǒ(.*?)(;.*)$/$1o$2③$3/"
    - "xform/^(.*)ò(.*?)(;.*)$/$1o$2④$3/"
    - "xform/^(.*)ē(.*?)(;.*)$/$1e$2①$3/"
    - "xform/^(.*)é(.*?)(;.*)$/$1e$2②$3/"
    - "xform/^(.*)ě(.*?)(;.*)$/$1e$2③$3/"
    - "xform/^(.*)è(.*?)(;.*)$/$1e$2④$3/"
    - "xform/^(.*)ī(.*?)(;.*)$/$1i$2①$3/"
    - "xform/^(.*)í(.*?)(;.*)$/$1i$2②$3/"
    - "xform/^(.*)ǐ(.*?)(;.*)$/$1i$2③$3/"
    - "xform/^(.*)ì(.*?)(;.*)$/$1i$2④$3/"
    - "xform/^(.*)ū(.*?)(;.*)$/$1u$2①$3/"
    - "xform/^(.*)ú(.*?)(;.*)$/$1u$2②$3/"
    - "xform/^(.*)ǔ(.*?)(;.*)$/$1u$2③$3/"
    - "xform/^(.*)ù(.*?)(;.*)$/$1u$2④$3/"
    - "xform/^(.*)ǖ(.*?)(;.*)$/$1v$2①$3/"
    - "xform/^(.*)ǘ(.*?)(;.*)$/$1v$2②$3/"
    - "xform/^(.*)ǚ(.*?)(;.*)$/$1v$2③$3/"
    - "xform/^(.*)ǜ(.*?)(;.*)$/$1v$2④$3/"
    - "xform/^(.*)ü(.*?)$/$1v$2/"
    - "xform/^(.*)ń(.*?)(;.*)$/$1n$2②$3/"
    - "xform/^(.*)ň(.*?)(;.*)$/$1n$2③$3/"
    - "xform/^(.*)ǹ(.*?)(;.*)$/$1n$2④$3/"
    - "xlit/①②③④/7890"
    - "derive/^ng(\\d)(;.*)$/eng$1$2/"
    - "xform/^n(\\d)(;.*)/en$1$2/"
    - "derive/^([jqxy])u(\\d)(;.*)$/$1v$2$3/"
    - "xform/^([aoe].*)(\\d)(;.*)$/o$1$2$3/"
    - "xform/^(\\w+?)en(\\d)(;.*)$/$1Ⓦ$2$3/"
    - "xform/^(\\w+?)eng(\\d)(;.*)$/$1Ⓣ$2$3/"
    - "xform/^(\\w+?)in(\\d)(;.*)$/$1Ⓨ$2$3/"
    - "xform/^(\\w+?)uai(\\d)(;.*)$/$1Ⓨ$2$3/"
    - "xform/^(\\w+?)uo(\\d)(;.*)$/$1Ⓞ$2$3/"
    - "xform/^(\\w+?)ai(\\d)(;.*)$/$1Ⓟ$2$3/"
    - "xform/^(\\w+?)[iu]ang(\\d)(;.*)$/$1Ⓖ$2$3/"
    - "xform/^(\\w+?)ang(\\d)(;.*)$/$1Ⓢ$2$3/"
    - "xform/^(\\w+?)ie(\\d)(;.*)$/$1Ⓓ$2$3/"
    - "xform/^(\\w+?)ian(\\d)(;.*)$/$1Ⓕ$2$3/"
    - "xform/^(\\w+?)i?ong(\\d)(;.*)$/$1Ⓗ$2$3/"
    - "xform/^(\\w+?)er(\\d)(;.*)$/$1Ⓙ$2$3/"
    - "xform/^(\\w+?)iu(\\d)(;.*)$/$1Ⓙ$2$3/"
    - "xform/^(\\w+?)ei(\\d)(;.*)$/$1Ⓚ$2$3/"
    - "xform/^(\\w+?)uan(\\d)(;.*)$/$1Ⓛ$2$3/"
    - "xform/^(\\w+?)ing(\\d)(;.*)$/$1◯$2$3/"
    - "xform/^(\\w+?)ou(\\d)(;.*)$/$1Ⓩ$2$3/"
    - "xform/^(\\w+?)[iu]a(\\d)(;.*)$/$1Ⓧ$2$3/"
    - "xform/^(\\w+?)iao(\\d)(;.*)$/$1Ⓑ$2$3/"
    - "xform/^(\\w+?)ue(\\d)(;.*)$/$1Ⓝ$2$3/"
    - "xform/^(\\w+?)ui(\\d)(;.*)$/$1Ⓝ$2$3/"
    - "xform/^(\\w+?)ve(\\d)(;.*)$/$1Ⓝ$2$3/"
    - "xform/^(\\w+?)un(\\d)(;.*)$/$1Ⓜ$2$3/"
    - "xform/^(\\w+?)ao(\\d)(;.*)$/$1Ⓠ$2$3/"
    - "xform/^(\\w+?)an(\\d)(;.*)$/$1Ⓡ$2$3/"
    - "xform/^zh/Ⓤ/"
    - "xform/^sh/Ⓘ/"
    - "xform/^ch/Ⓐ/"
    - "xlit/ⓌⓉⓎⓊⒾⓄⓅⒶⒼⓈⒹⒻⒽⒿⓀⓁⓏⓍⒷⓃⓂⓆⓇ/wtyuiopagsdfhjklzxbnmqr/"
"自然码":
  __append:
    - "xform/^([a-z]+)(;.*?)$/$1④$2/"
    - "xform/^(.*)ā(.*?)(;.*)$/$1a$2①$3/"
    - "xform/^(.*)á(.*?)(;.*)$/$1a$2②$3/"
    - "xform/^(.*)ǎ(.*?)(;.*)$/$1a$2③$3/"
    - "xform/^(.*)à(.*?)(;.*)$/$1a$2④$3/"
    - "xform/^(.*)ō(.*?)(;.*)$/$1o$2①$3/"
    - "xform/^(.*)ó(.*?)(;.*)$/$1o$2②$3/"
    - "xform/^(.*)ǒ(.*?)(;.*)$/$1o$2③$3/"
    - "xform/^(.*)ò(.*?)(;.*)$/$1o$2④$3/"
    - "xform/^(.*)ē(.*?)(;.*)$/$1e$2①$3/"
    - "xform/^(.*)é(.*?)(;.*)$/$1e$2②$3/"
    - "xform/^(.*)ě(.*?)(;.*)$/$1e$2③$3/"
    - "xform/^(.*)è(.*?)(;.*)$/$1e$2④$3/"
    - "xform/^(.*)ī(.*?)(;.*)$/$1i$2①$3/"
    - "xform/^(.*)í(.*?)(;.*)$/$1i$2②$3/"
    - "xform/^(.*)ǐ(.*?)(;.*)$/$1i$2③$3/"
    - "xform/^(.*)ì(.*?)(;.*)$/$1i$2④$3/"
    - "xform/^(.*)ū(.*?)(;.*)$/$1u$2①$3/"
    - "xform/^(.*)ú(.*?)(;.*)$/$1u$2②$3/"
    - "xform/^(.*)ǔ(.*?)(;.*)$/$1u$2③$3/"
    - "xform/^(.*)ù(.*?)(;.*)$/$1u$2④$3/"
    - "xform/^(.*)ǖ(.*?)(;.*)$/$1v$2①$3/"
    - "xform/^(.*)ǘ(.*?)(;.*)$/$1v$2②$3/"
    - "xform/^(.*)ǚ(.*?)(;.*)$/$1v$2③$3/"
    - "xform/^(.*)ǜ(.*?)(;.*)$/$1v$2④$3/"
    - "xform/^(.*)ü(.*?)$/$1v$2/"
    - "xform/^(.*)ń(.*?)(;.*)$/$1n$2②$3/"
    - "xform/^(.*)ň(.*?)(;.*)$/$1n$2③$3/"
    - "xform/^(.*)ǹ(.*?)(;.*)$/$1n$2④$3/"
    - "xlit/①②③④/7890"
    - "derive/^ng(\\d)(;.*)$/eng$1$2/"
    - "xform/^n(\\d)(;.*)/en$1$2/"
    - "derive/^([jqxy])u(\\d)(;.*)$/$1v$2$3/"
    - "derive/^([aoe])([ioun])(\\d)(;.*)$/$1$1$2$3$4/"
    - "xform/^([aoe])(ng)?(\\d)(;.*)$/$1$1$2$3$4/"
    - "xform/^(\\w+?)iu(\\d)(;.*)$/$1Ⓠ$2$3/"
    - "xform/^(\\w+?)[uv]an(\\d)(;.*)$/$1Ⓡ$2$3/"
    - "xform/^(\\w+?)[uv]e(\\d)(;.*)$/$1Ⓣ$2$3/"
    - "xform/^(\\w+?)ing(\\d)(;.*)$/$1Ⓨ$2$3/"
    - "xform/^(\\w+?)uai(\\d)(;.*)$/$1Ⓨ$2$3/"
    - "xform/^(\\w+?)uo(\\d)(;.*)$/$1Ⓞ$2$3/"
    - "xform/^(\\w+?)[uv]n(\\d)(;.*)$/$1Ⓟ$2$3/"
    - "xform/^(\\w+?)i?ong(\\d)(;.*)$/$1Ⓢ$2$3/"
    - "xform/^(\\w+?)[iu]ang(\\d)(;.*)$/$1Ⓓ$2$3/"
    - "xform/^(\\w+?)en(\\d)(;.*)$/$1Ⓕ$2$3/"
    - "xform/^(\\w+?)eng(\\d)(;.*)$/$1Ⓖ$2$3/"
    - "xform/^(\\w+?)ang(\\d)(;.*)$/$1Ⓗ$2$3/"
    - "xform/^(\\w+?)ian(\\d)(;.*)$/$1Ⓜ$2$3/"
    - "xform/^(\\w+?)an(\\d)(;.*)$/$1Ⓙ$2$3/"
    - "xform/^(\\w+?)iao(\\d)(;.*)$/$1Ⓒ$2$3/"
    - "xform/^(\\w+?)ao(\\d)(;.*)$/$1Ⓚ$2$3/"
    - "xform/^(\\w+?)ai(\\d)(;.*)$/$1Ⓛ$2$3/"
    - "xform/^(\\w+?)ei(\\d)(;.*)$/$1Ⓩ$2$3/"
    - "xform/^(\\w+?)ie(\\d)(;.*)$/$1Ⓧ$2$3/"
    - "xform/^(\\w+?)ui(\\d)(;.*)$/$1Ⓥ$2$3/"
    - "xform/^(\\w+?)ou(\\d)(;.*)$/$1Ⓑ$2$3/"
    - "xform/^(\\w+?)in(\\d)(;.*)$/$1Ⓝ$2$3/"
    - "xform/^(\\w+?)[iu]a(\\d)(;.*)$/$1Ⓦ$2$3/"
    - "xform/^sh/Ⓤ/"
    - "xform/^ch/Ⓘ/"
    - "xform/^zh/Ⓥ/"
    - "xlit/ⓆⓌⓇⓉⓎⓊⒾⓄⓅⓈⒹⒻⒼⒽⓂⒿⒸⓀⓁⓏⓍⓋⒷⓃ/qwrtyuiopsdfghmjcklzxvbn/"
"自然龙":
  __append:
    - "xform/^ēr/eQ/"
    - "xform/^ér/eK/"
    - "xform/^ěr/eU/"
    - "xform/^èr/eH/"
    - "xform/^er/eQ/"
    - "xform/^(ā|á|ǎ|à)([ioun])/a$1$2/"
    - "xform/^(ō|ó|ǒ|ò)([ioun])/o$1$2/"
    - "xform/^(ē|é|ě|è)([ioun])/e$1$2/"
    - "xform/^(ā|á|ǎ|à)(ng)/a$1$2/"
    - "xform/^(ō|ó|ǒ|ò)(ng)/o$1$2/"
    - "xform/^(ē|é|ě|è)(ng)/e$1$2/"
    - "xform/^(ā|á|ǎ|à)/a$1/"
    - "xform/^(ō|ó|ǒ|ò)/o$1/"
    - "xform/^(ē|é|ě|è)/e$1/"
    - "xform/^([jqxy])u(;.*)/$1ü$2/"
    - "xform/^([jqxy])ū(;.*)/$1ǖ$2/"
    - "xform/^([jqxy])ú(;.*)/$1ǘ$2/"
    - "xform/^([jqxy])ǔ(;.*)/$1ǚ$2/"
    - "xform/^([jqxy])ù(;.*)/$1ǜ$2/"
    - "xform/^a(;.*)$/aā$1/"
    - "xform/^o(;.*)$/oō$1/"
    - "xform/^e(;.*)$/eē$1/"
    - "xform/^ǹg/eèng/"
    - "xform/^ňg/eěng/"
    - "xform/^ńg/eéng/"
    - "xform/^ng/eeng/"
    - "xform/^ǹ/eèn/"
    - "xform/^ň/eěn/"
    - "xform/^ń/eén/"
    - "xform/^n(;.*)/een$1/"
    - "xform/^sh/U/"
    - "xform/^ch/I/"
    - "xform/^zh/V/"
    - "xform/^(.)(iáo|iǎng|uǎng|ang|āng|ue|uē|üe|ǖe|ǎi|á)(;.*)$/$1U$3/"
    - "xform/^(.)(iàng|iǒng|uàng|ēn|en|īng|ing|é|ó)(;.*)$/$1E$3/"
    - "xform/^(.)(iǎn|iōng|iong|uǎi|uò|ǎng|ō|o)(;.*)$/$1P$3/"
    - "xform/^(.)(uāng|uang|ǐng|ìng|uí|áng)(;.*)$/$1W$3/"
    - "xform/^(.)(uǎn|uái||uā|ua|én|uō|uo|ié|ǚ)(;.*)$/$1S$3/"
    - "xform/^(.)(uán|ài|ěn|èn|uě|ǚe|ǎn|ǔn|iù)(;.*)$/$1O$3/"
    - "xform/^(.)(uān|uan|àng|ái|iā|ia|uè|üè)(;.*)$/$1D$3/"
    - "xform/^(.)(iáng|áo|ué|üé|ēi|ei|à|è|ǒ)(;.*)$/$1I$3/"
    - "xform/^(.)(uāi|uai|uà|uǎ|ūn|un|ò|ǐ)(;.*)$/$1G$3/"
    - "xform/^(.)(éng|èng|uài|èi|uì|ǜ|ún)(;.*)$/$1F$3/"
    - "xform/^(.)(ióng|ōng|ong|án|iē|ie)(;.*)$/$1K$3/"
    - "xform/^(.)(iào|iǎo|uǒ|uó|a|ā|ě|ú)(;.*)$/$1L$3/"
    - "xform/^(.)(uàn|ēng|eng|iá|ín|iě)(;.*)$/$1C$3/"
    - "xform/^(.)(iān|ian|òu|éi|ùn|ē|e)(;.*)$/$1R$3/"
    - "xform/^(.)(iāng|iang|ěng|òng)(;.*)$/$1Y$3/"
    - "xform/^(.)(iao|iāo|ǔ|ǎ|iú|ǘ)(;.*)$/$1M$3/"
    - "xform/^(.)(iǎ|íng|ān|an|ǒng)(;.*)$/$1N$3/"
    - "xform/^(.)(iòng|īn|in|ǖ|ü|ù)(;.*)$/$1H$3/"
    - "xform/^(.)(ǎo|ià|ǐn|ōu|ou)(;.*)$/$1X$3/"
    - "xform/^(.)(óng|àn|ěi|ī|i)(;.*)$/$1J$3/"
    - "xform/^(.)(ián|ào|ìn|uǐ)(;.*)$/$1V$3/"
    - "xform/^(.)(uáng|āi|ai|í)(;.*)$/$1B$3/"
    - "xform/^(.)(ǒu|iū|iu|iǔ)(;.*)$/$1Z$3/"
    - "xform/^(.)(uá|uī|ui|ì)(;.*)$/$1T$3/"
    - "xform/^(.)(ū|u|óu|iàn)(;.*)$/$1A$3/"
    - "xform/^(.)(āo|ao|iè)(;.*)$/$1Q$3/"
    - "xlit/QWERTYUIOPASDFGHMJCKLZXVBN/qwertyuiopasdfghmjcklzxvbn/"
"间接辅助":
  __append:
    - "derive/^(.*?)(\\d?);.*$/$1/"
    - "derive/^(.*?)(\\d?);.*$/$1$2/"
    - "derive|^(.*?)(\\d?);(\\w).*$|$1/$3|"
    - "derive|^(.*?)(\\d?);(\\w).*$|$1$2/$3|"
    - "derive|^(.*?)(\\d?);(\\w)(\\w).*$|$1/$3$4|"
    - "derive|^(.*?)(\\d?);(\\w)(\\w).*$|$1$2/$3$4|"
    - "derive|^(.*?)(\\d?);.*?,(\\w).*$|$1/$3|"
    - "derive|^(.*?)(\\d?);.*?,(\\w).*$|$1$2/$3|"
    - "derive|^(.*?)(\\d?);.*?,(\\w)(\\w).*$|$1/$3$4|"
    - "derive|^(.*?)(\\d?);.*?,(\\w)(\\w).*$|$1$2/$3$4|"
    - "derive|^(.*?)(\\d?);.*?,.*?,(\\w).*$|$1/$3|"
    - "derive|^(.*?)(\\d?);.*?,.*?,(\\w).*$|$1$2/$3|"
    - "derive|^(.*?)(\\d?);.*?,.*?,(\\w)(\\w).*$|$1/$3$4|"
    - "derive|^(.*?)(\\d?);.*?,.*?,(\\w)(\\w).*$|$1$2/$3$4|"
    - "derive|^(.*?)(\\d?);.*?,.*?,.*?,(\\w).*$|$1/$3|"
    - "derive|^(.*?)(\\d?);.*?,.*?,.*?,(\\w).*$|$1$2/$3|"
    - "derive|^(.*?)(\\d?);.*?,.*?,.*?,(\\w)(\\w).*$|$1/$3$4|"
    - "derive|^(.*?)(\\d?);.*?,.*?,.*?,(\\w)(\\w).*$|$1$2/$3$4|"
    - "xform/◯/;/"
    - "erase/^(.+);(.+)$/"
"龙三":
  __append:
    - "xform/^ēr/e¥/"
    - "xform/^ér/e¥/"
    - "xform/^ěr/e¥/"
    - "xform/^èr/e&/"
    - "xform/^er/e&/"
    - "xform/^(ā|á|ǎ|à)([ioun])/a$1$2/"
    - "xform/^(ō|ó|ǒ|ò)([ioun])/o$1$2/"
    - "xform/^(ē|é|ě|è)([ioun])/e$1$2/"
    - "xform/^(ā|á|ǎ|à)(ng)/a$1$2/"
    - "xform/^(ō|ó|ǒ|ò)(ng)/o$1$2/"
    - "xform/^(ē|é|ě|è)(ng)/e$1$2/"
    - "xform/^(ā|á|ǎ|à)/a$1/"
    - "xform/^(ō|ó|ǒ|ò)/o$1/"
    - "xform/^(ē|é|ě|è)/e$1/"
    - "xform/^([jqxy])u(;.*)/$1ü$2/"
    - "xform/^([jqxy])ū(;.*)/$1ǖ$2/"
    - "xform/^([jqxy])ú(;.*)/$1ǘ$2/"
    - "xform/^([jqxy])ǔ(;.*)/$1ǚ$2/"
    - "xform/^([jqxy])ù(;.*)/$1ǜ$2/"
    - "xform/^a(;.*)$/aā$1/"
    - "xform/^o(;.*)$/oō$1/"
    - "xform/^e(;.*)$/eē$1/"
    - "xform/^ǹg/nèng/"
    - "xform/^ňg/něng/"
    - "xform/^ńg/néng/"
    - "xform/^ng/neng/"
    - "xform/^ǹ/eèn/"
    - "xform/^ň/eěn/"
    - "xform/^ń/eén/"
    - "xform/^n(;.*)/een$1/"
    - "xform/^sh/U/"
    - "xform/^ch/I/"
    - "xform/^zh/V/"
    - "xform/^y/E/"
    - "xform/^p/Y/"
    - "xform/^e/P/"
    - "xform/^(.)(āo|àng|ang|ióng|iǒng|iú|iǔ|iù|iu|uǎ|uá)(;.*)$/$1Q$3/"
    - "xform/^(.)(iā|uàng|uang|ó|ǒ|òu|ou|ún|ǔn)(;.*)$/$1W$3/"
    - "xform/^(.)(iān|é|ě)(;.*)$/$1E$3/"
    - "xform/^(.)(|án|ǎn|iào|iao)(;.*)$/$1R$3/"
    - "xform/^(.)(áng|ǎng|ián|iǎn|uài|uai)(;.*)$/$1T$3/"
    - "xform/^(.)(ìng|ing|ú|ǔ)(;.*)$/$1Y$3/"
    - "xform/^(.)(è|e|ēi|iē|iū)(;.*)$/$1U$3/"
    - "xform/^(.)(ā|ào|uī|ǖ|iàng|iang)(;.*)$/$1I$3/"
    - "xform/^(.)(í|ǐ|uó|uǒ)(;.*)$/$1O$3/"
    - "xform/^(.)(iāng|ù|u|ǘ|ǚ)(;.*)$/$1P$3/"
    - "xform/^(.)(ì|i|uà|ua)(;.*)$/$1A$3/"
    - "xform/^(.)(àn|an|iáng|iǎng|iòng|iong|uǐ|uí|ūn)(;.*)$/$1S$3/"
    - "xform/^(.)(éng|ěng|īng|uā|ùn|un)(;.*)$/$1D$3/"
    - "xform/^(.)(āi|ēng|iá|iǎ|ín|ǐn|iōng|uàn|uan|ér|ěr)(;.*)$/$1F$3/"
    - "xform/^(.)(íng|ǐng|ū)(;.*)$/$1G$3/"
    - "xform/^(.)(ài|ai|iāo|uán|uǎn)(;.*)$/$1H$3/"
    - "xform/^(.)(én|ěn|òng|ong|uò|uo|üé|ǚe|ué|uě)(;.*)$/$1J$3/"
    - "xform/^(.)(èi|ei|ōng|in|ìn|uō|üè|üe|uè|ue)(;.*)$/$1K$3/"
    - "xform/^(.)(éi|ěi|ī|uáng|uǎng)(;.*)$/$1L$3/"
    - "xform/^(.)(áo|ǎo|ié|iě|uāng|ǖe|uē)(;.*)$/$1Z$3/"
    - "xform/^(.)(āng|iáo|iǎo|ò|o|uái|uǎi)(;.*)$/$1X$3/"
    - "xform/^(.)(á|ǎ|ái|ǎi|ià|ia|ōu|uāi|èr|er)(;.*)$/$1C$3/"
    - "xform/^(.)(èng|eng|ō|ǜ|ü|uì|ui)(;.*)$/$1V$3/"
    - "xform/^(.)(ān|ē|iè|ie)(;.*)$/$1B$3/"
    - "xform/^(.)(èn|en|iàn|ian|óu|ǒu|uān)(;.*)$/$1N$3/"
    - "xform/^(.)(à|a|ēn|īn|óng|ǒng)(;.*)$/$1M$3/"
    - "xlit/QWERTYUIOPASDFGHMJCKLZXVBN¥&/qwertyuiopasdfghmjcklzxvbnfc/"